{
  "extends": "@vue/tsconfig/tsconfig.json",
  "compilerOptions": {
    "lib": ["esnext", "dom"],
    "baseUrl": ".",
    "paths": {
      "~/*": ["./src/*"],
      "#/*": ["./types/*"]
    },
    "types": [
      "@dcloudio/types",
      "wot-design-uni/global.d.ts",
      "@uni-helper/uni-app-types"
    ],
    "sourceMap": true
  },
  "vueCompilerOptions": {
    // 调整 Volar（Vue 语言服务工具）解析行为，用于为 uni-app 组件提供 TypeScript 类型
    "plugins": ["@uni-helper/uni-app-types/volar-plugin"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts"
  ],
  "exclude": ["eslint.config.js"]
}
