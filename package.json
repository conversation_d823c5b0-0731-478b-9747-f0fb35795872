{"name": "uniapp-starter", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.3", "license": "MIT", "scripts": {"dev": "uni", "dev:wx": "uni -p mp-weixin", "build": "uni build -p h5", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@bryce-loskie/use": "^0.4.9", "@bryce-loskie/utils": "^0.3.7", "@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-harmony": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-alipay": "3.0.0-4030620241128001", "@dcloudio/uni-mp-baidu": "3.0.0-4030620241128001", "@dcloudio/uni-mp-jd": "3.0.0-4030620241128001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4030620241128001", "@dcloudio/uni-mp-lark": "3.0.0-4030620241128001", "@dcloudio/uni-mp-qq": "3.0.0-4030620241128001", "@dcloudio/uni-mp-toutiao": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "@dcloudio/uni-mp-xhs": "3.0.0-4030620241128001", "@dcloudio/uni-quickapp-webview": "3.0.0-4030620241128001", "@tanstack/vue-query": "4.37.1", "@vueuse/core": "^10.11.0", "cos-js-sdk-v5": "^1.10.1", "dayjs": "^1.11.13", "jswx": "^1.6.0", "klona": "^2.0.6", "modern-screenshot": "^4.6.5", "motion-v": "^1.3.0", "numeral": "^2.0.6", "pinia": "2.0.36", "pinia-plugin-persist-uni": "^1.3.1", "query-string": "^9.2.1", "ufo": "^1.6.1", "vue": "^3.5.17", "wot-design-uni": "^1.9.1", "zod": "^3.25.67"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@bryce-loskie/unocss-preset": "^0.1.1", "@dcloudio/types": "^3.4.15", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/uni-vue-devtools": "3.0.0-alpha-4000020240111001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@iconify-json/carbon": "^1.2.10", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.4", "@types/numeral": "^2.0.5", "@uni-helper/uni-app-types": "1.0.0-alpha.3", "@uni-helper/uni-env": "^0.1.7", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@unocss/eslint-config": "~66.3.2", "@unocss/eslint-plugin": "^66.3.2", "@vue/runtime-core": "^3.5.17", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "lint-staged": "^16.1.2", "lodash-es": "^4.17.21", "prettier": "^3.6.1", "sass": "^1.89.2", "taze": "^19.1.0", "typescript": "^5.8.3", "unocss": "0.58.9", "unplugin-auto-import": "^19.3.0", "vite": "5.2.8", "vue-tsc": "^2.2.10"}}