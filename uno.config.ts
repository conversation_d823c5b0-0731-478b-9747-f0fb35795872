import { presetMini } from '@bryce-loskie/unocss-preset/mini'
import { defineConfig, presetTypography, transformerDirectives, transformerVariantGroup } from 'unocss'

export default defineConfig({
  presets: [
    presetMini({
      useIcon: true,
      useShortcuts: true,
      useCdnIcon: true,
    }),
    presetTypography(),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  rules: [
    ['no-callout', { '-webkit-touch-callout': 'none' }],
  ],
})
