import { VueQueryPlugin } from '@tanstack/vue-query'

import { createSSRApp } from 'vue'
import App from './App.vue'
import { shareMixin } from './mixins'
import store from './store'
import 'uno.css'
import '@unocss/reset/tailwind-compat.css'

export function createApp() {
  const app = createSSRApp(App)

  app.mixin(shareMixin)
  app.use(store)
  app.use(VueQueryPlugin)
  // WX.config({
  //   debug: true,
  //   appId: 'wxcb1ae19acb9e019f',
  //   nonceStr: 'abcd',
  //   timestamp: 1752413552,
  //   url: 'http://192.168.1.2:5173',
  //   signature: 'e3781f439d21c268d12243094eaeb24b3de9a8d1',
  //   jsApiList: ['chooseImage'],
  // })

  return {
    app,
  }
}
