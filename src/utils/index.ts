import dayjs from 'dayjs'
import numeral from 'numeral'
import { unref } from 'vue'

export const validateForm = (formRef: any) => {
  return new Promise<void>((resolve, reject) => {
    unref(formRef).validate().then(({ valid }: any) => {
      if (!valid)
        return reject(new Error('表单验证失败，请检查输入内容。'))

      resolve()
    })
  })
}

export const handleNavigateBack = () => {
  // #ifdef H5
  window.history.back()
  // #endif

  // #ifndef H5
  uni.navigateBack().catch(() => {
    uni.switchTab({
      url: '/pages/index/index',
    })
  })
  // #endif
}

export const getWeixinLoginCode = () => {
  return new Promise<string>((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: ({ code }) => resolve(code),
      fail: err => reject(err),
    })
  })
}

export const handleComingSoon = () => {
  uni.showToast({
    title: '正在完善 ~',
  })
}

export const formatTime = (time: any) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : time
}

export const formatPercent = (val?: string | number) => {
  return numeral(val).format('0.000%')
}

export const normalizeNumber = (val?: number) => {
  const num = Number(val)
  if (Number.isNaN(num))
    return 0

  return +(num || 0).toFixed(2)
}

export const formatPrice = (price?: number | string) => {
  const raw = `￥${numeral(price).format('0,0[.]000')}`
  if (raw.includes('.'))
    return raw.replace(/0+$/, '')
  return raw
}

export const safeJsonParse = <T = Record<string, any>>(str?: string | null, defaultValue: T = {} as T): T => {
  if (!str) {
    return defaultValue
  }
  try {
    return JSON.parse(str)
  }
  // eslint-disable-next-line unused-imports/no-unused-vars
  catch (error) {
    return defaultValue
  }
}
