import type { App, Component, VNode, VNodeChild } from 'vue'

import { createVNode, nextTick, render } from 'vue'
import { uuid } from '~/utils/uuid'

// ✅ 基础可拓展 props 类型
interface BaseProps {
  title?: string
  close?: () => void
  onSuccess?: (result?: any) => void
  onError?: (error?: any) => void
  destroy?: () => void
  el?: HTMLElement
  transitionSelector?: string
  options?: Record<string, any>
  // 新增插槽支持
  slots?: Record<string, () => VNodeChild | VNodeChild[]>
}

// ✅ 返回值结构
interface CreateFuncCompReturn {
  close: () => Promise<void>
}

// ✅ 主函数，使用泛型参数
function createFuncComp<P extends BaseProps & Record<string, any> = BaseProps>(
  Components: Component,
  transitionSelector: string = '',
) {
  return function (option: P = {} as P): CreateFuncCompReturn {
    const name = Components.name || 'anonymous'
    const uuidStr = `${uuid('xxxxxxxxxxxxxxxxxxxx')}-${name}`

    const div = document.createElement('div')
    div.className = uuidStr
    document.body.appendChild(div)

    let hasClosed = false
    let transitionEnded = false

    const cleanup = () => {
      if (!div.isConnected)
        return
      render(null, div)
      div.parentElement?.removeChild(div)
    }

    const destroy = () => {
      const selector = transitionSelector
      if (!selector) {
        setTimeout(() => {
          cleanup()
        }, 1500)
        return
      }
      const transitionEl = div.querySelector(selector) as HTMLElement | null

      if (transitionEl) {
        const handleTransitionEnd = () => {
          if (transitionEnded)
            return
          transitionEnded = true
          transitionEl.removeEventListener('animationend', handleTransitionEnd)
          transitionEl.removeEventListener('transitionend', handleTransitionEnd)
          cleanup()
        }

        transitionEl.addEventListener('animationend', handleTransitionEnd, { once: true })
        transitionEl.addEventListener('transitionend', handleTransitionEnd, { once: true })

        setTimeout(() => {
          if (!transitionEnded) {
            handleTransitionEnd()
          }
        }, 1500)
      }
      else {
        setTimeout(() => {
          cleanup()
        }, 1500)
      }
    }

    const { slots, ...restOption } = option
    const props: Omit<P, 'slots'> = {
      ...restOption,
      destroy,
    }

    // 创建 VNode 时传入插槽
    const vnode: VNode = createVNode(Components, props, slots)
    render(vnode, div)

    async function close(): Promise<void> {
      if (hasClosed)
        return
      hasClosed = true

      await nextTick()

      const instance = vnode.component
      const exposed = instance?.exposed

      if (typeof exposed?.beforeClose === 'function') {
        exposed.beforeClose()
      }

      destroy()
    }

    return { close }
  }
}
interface EventShim {
  new (...args: any[]): {
    $props: {
      onClick?: (...args: any[]) => void
    }
  }
}

export type WithInstall<T> = T & {
  install: (app: App) => void
} & EventShim

export type CustomComponent = Component & { displayName?: string }

export const withInstall = <T extends CustomComponent>(component: T, alias?: string) => {
  (component as Record<string, unknown>).install = (app: App) => {
    const compName = component.name || component.displayName
    if (!compName)
      return
    app.component(compName, component)
    if (alias) {
      app.config.globalProperties[alias] = component
    }
  }
  return component as WithInstall<T>
}

export { createFuncComp }
export type { BaseProps as Props }
