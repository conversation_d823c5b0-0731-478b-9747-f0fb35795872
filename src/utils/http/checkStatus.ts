import { get, size } from 'lodash-es'
// 服务器状态码返回错误处理
export async function handleStatusError(error: Request.resType, reject: any) {
  const { statusCode } = error
  const msg
    = size(get(error, 'data.msg', '')) <= 15 ? get(error, 'data.msg', '') : ''

  try {
    switch (statusCode) {
      case 401:
      case 424:
      case 9001:
        showToastMsg({ showToast: true, isSuccess: false, msg: '登录过期' })
        reLogin()
        break
      case 400:
        showToastMsg({ showToast: true, isSuccess: false, msg: '参数错误' })
        break
      case 403:
        showToastMsg({ showToast: true, isSuccess: false, msg: '没有权限' })
        break
      case 404:
        showToastMsg({ showToast: true, isSuccess: false, msg: '接口不存在' })
        break
      case 405:
        showToastMsg({
          showToast: true,
          isSuccess: false,
          msg: '请求方式错误',
        })
        break
      case 408:
        showToastMsg({ showToast: true, isSuccess: false, msg: '请求超时' })
        break
      case 500:
        showToastMsg({
          showToast: true,
          isSuccess: false,
          msg: msg || '服务器错误',
        })
        break
      case 501:
        showToastMsg({ showToast: true, isSuccess: false, msg: '服务未实现' })
        break
      case 502:
        showToastMsg({ showToast: true, isSuccess: false, msg: '网关错误' })
        break
      case 503:
        showToastMsg({ showToast: true, isSuccess: false, msg: '服务不可用' })
        break
      case 504:
        showToastMsg({ showToast: true, isSuccess: false, msg: '网关超时' })
        break
      case 505:
        showToastMsg({
          showToast: true,
          isSuccess: false,
          msg: 'HTTP版本不受支持',
        })
        break
      default:
        showToastMsg({ showToast: true, isSuccess: false, msg: '未知错误' })
        return reject(error)
    }
    return reject(error)
  }
  catch (error) {
    console.log('error 捕捉错误', error)
    return reject(error)
  }
}

// 返回成功code 200 但是接口报错 例如：参数错误
export async function handleError(
  error: Request.resDataType,
  reject: any,
  data1: Request.httpType,
) {
  const { code, msg } = error
  const { showToast = false } = data1
  // 业务逻辑处理
  try {
    // 业务逻辑处理
    // 例如：token过期
    // switch
    switch (code) {
      case 401:
      case 9001:
        // TODO:重新登录
        reLogin()
        break
      case -1:
        showToastMsg({
          showToast,
          isSuccess: false,
          msg: msg?.length <= 15 ? msg : undefined,
        })
        break
      case 1:
        showToastMsg({
          showToast,
          isSuccess: false,
          msg: msg?.length <= 30 ? msg : undefined,
        })
        break
      default:
        showToastMsg({ showToast, isSuccess: false })
        break
    }

    return reject(error)
  }
  catch (error) {
    return reject(error)
  }
}

// TODO: 重新登录
function reLogin() {
  console.log('reLogin')
}

// 显示接口返回结果
export function showToastMsg({
  showToast,
  msg,
  isSuccess = true,
}: Request.showToastMsgType) {
  if (showToast) {
    const title = msg || (isSuccess ? '操作成功' : '操作失败')
    uni.showToast({
      title,
      icon: 'none',
      duration: 3000,
    })
  }
}
