import queryString from 'query-string'

export function getQueryString({
  method,
  data,
  params,
  url,
}: Request.httpType) {
  if (method === 'GET' && params) {
    return `${url}?${queryString.stringify(params)}`
  }
  else if (method === 'GET' && data) {
    return `${url}?${queryString.stringify(data)}`
  }
  if (params) {
    return `${url}?${queryString.stringify(params)}`
  }
  return url
}
