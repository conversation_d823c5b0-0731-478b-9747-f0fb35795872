import { get } from 'lodash-es'
import { StrToJson } from '~/utils/tools'

const commoneRequestUrl = import.meta.env.VITE_BASEURL // 接口统一访问链接
export function uploadFile(options: Request.httpUpload) {
  return new Promise((resolve, reject) => {
    const jwtToken = ''
    if (jwtToken) {
      options.header = {
        ...options.header,
        Authorization: `Bearer ${jwtToken}`,
      }
    }
    console.log('上传前参数', options)

    uni.uploadFile({
      ...options,
      url: `${commoneRequestUrl}${options.url}`,
      success: (res) => {
        // 判断上传是否成功，视具体的业务逻辑而定
        if (res.statusCode === 200) {
          const data = get(StrToJson(get(res, 'data', '')), 'data')
          resolve(data) // 上传成功
        }
        else {
          reject(
            new Error(
              `Upload failed with status ${res.statusCode}: ${res.data}`,
            ),
          )
        }
      },
      fail: (err) => {
        reject(err) // 上传失败
      },
    })
  })
}
