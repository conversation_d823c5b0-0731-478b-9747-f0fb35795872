export function StrToJson(str: string): Record<string, any> {
  if (isJson(str)) {
    return JSON.parse(str)
  }
  else {
    return {}
  }
}

export function isJson(str: string) {
  if (typeof str == 'string') {
    try {
      const obj = JSON.parse(str)
      if (typeof obj == 'object' && obj) {
        return true
      }
      else {
        return false
      }
    }
    catch (e) {
      console.log(`error：${str}!!!${e}`)
      return false
    }
  }
  return false
}

export function maskPhoneNumber(input: string) {
  if (!input) {
    return input
  }
  // 定义手机号正则（以中国大陆手机号为例）
  const phoneRegex = /^1[3-9]\d{9}$/

  // 判断是否为手机号
  if (phoneRegex.test(input)) {
    // 如果是手机号，进行中间脱敏
    return input.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
  }
  else {
    // 如果不是手机号，返回原始字符串
    return input
  }
}
