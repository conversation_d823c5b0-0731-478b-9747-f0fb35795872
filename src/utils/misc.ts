import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

export const calcDuration = (inSeconds: number) => {
  const duration = dayjs.duration(inSeconds, 'seconds')
  const hours = duration.hours()
  const minutes = duration.minutes()
  const seconds = duration.seconds()

  const res: string[] = []
  if (hours)
    res.push(`${hours}小时`)
  if (minutes)
    res.push(`${minutes}分钟`)
  if (seconds)
    res.push(`${seconds}秒`)

  return res.join('')
}
