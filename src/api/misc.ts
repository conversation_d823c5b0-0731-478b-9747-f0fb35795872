export interface ILottery {
  prizeId: string
  prizeName: string
  prizeIndex: null
  stock: null
  probability: null
  pic: null
  enable: null
}

export enum MissionTypeEnum {
  Question = 'question',
  Photo = 'photo',
  Scan = 'scan',
  Click = 'click',
}

export interface IMission {
  missionId: string
  missionName: string
  missionType: MissionTypeEnum
  missionInfo?: string // json string, need to parse
  missionPrize: null
  createTime: null
  updateTime: null
  isFinished: null
}

export interface IQuestionMission {
  title: string
  question: {
    answer: { choice: string, info: string }[]
    bingo: string
  }[]
}

export interface IUserMissionInfo {
  hasLottery: boolean
  missionInfos: IMission[]
}

export const miscApi = {
  /**
   * 获取任务信息
   */
  getMissionInfo(missionId?: string) {
    return http<NormalResponse<IMission>>({
      url: `/getMissionInfo/${missionId}`,
      method: 'GET',
    })
  },

  /**
   * 获取活动规则
   */
  getActivityRules() {
    return http<NormalResponse<string>>({
      url: '/getRole',
      method: 'GET',
    })
  },

  getUserMissionListQueryKey: ['mission list'],

  /**
   * 用户获取自己的任务完成情况, 包括是否完成任务、是否抽奖等
   */
  getUserMissionStatus() {
    return http<NormalResponse<IUserMissionInfo>>({
      url: '/info',
      method: 'GET',
    })
  },

  /**
   * 获取奖品列表, 包含库存信息
   */
  getPrizeList() {
    return http<ListResponse<ILottery>>({
      url: '/lottery',
      method: 'GET',
    })
  },

  /**
   * 抽奖
   */
  drawPrize() {
    return http({
      url: `/lottery`,
      method: 'POST',
    })
  },

  /**
   * 用户完成任务, 更新任务完成情况
   */
  completeMission(missionId: string) {
    return http({
      url: `/mission?missionId=${missionId}`,
      method: 'PUT',
    })
  },

  /**
   * 用户获取自己的抽奖记录
   */
  getUserDrawRecord() {
    return http({
      url: '/queryMyPrize',
      method: 'GET',
    })
  },

  /**
   * check if user has received prize
   */
  checkIsReceived() {
    return http<NormalResponse<boolean>>({
      url: `/checkIsReceived`,
      method: 'GET',
    })
  },

  /**
   * 用户领取奖品
   */
  receivePrize() {
    return http({
      url: `/submitLottery`,
      method: 'GET',
    })
  },

  /**
   * 上报分享操作
   */
  shareFriend() {
    return http({
      url: '/shareFriend',
      method: 'GET',
    })
  },

  /**
   * 上报分享操作
   */
  shareSocial() {
    return http({
      url: '/shareSocial',
      method: 'GET',
    })
  },

  /**
   * 检查是否可以抽奖
   */
  checkLotteryStatus() {
    return http<NormalResponse<LotteryStatusEnum>>({
      url: '/checkLottery',
      method: 'GET',
    })
  },
  /**
   * 获取wx.config
   */
  getWxConfig() {
    // 获取 urlPath
    const urlPath = window.location.href.split('#')[0]
    return http({
      url: `/jsApi?url=${urlPath}`,
      method: 'GET',
    })
  },
}

/**
 * alreadyLottery 已抽奖过了,
 * notEnough: 未完成7个站点
 *
 * 如果是已抽奖的, 调用获取我的抽奖记录, 直接显示获取的奖品
 */
export enum LotteryStatusEnum {
  NotEnough = 'notEnough',
  AlreadyLottery = 'alreadyLottery',
}
