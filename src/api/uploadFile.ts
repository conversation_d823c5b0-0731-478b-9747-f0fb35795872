import COS from 'cos-js-sdk-v5'

const Bucket = 'third-1330359603'
const Region = 'ap-shanghai'
const SecretId = 'AKIDvhGbjgQwEJCuPgjYAZkhdLtOgnojVDuU'
const SecretKey = 'TilaCaliS1MUQAgz4xd7NQFzgGqMgqmV'
const cos = new COS({ SecretId, SecretKey })

function getExt(f: File) {
  const m = f.name.match(/\.([a-z0-9]+)$/i)
  return m ? `.${m[1]}` : '' // 没找到就返回空串
}

function getDisposition(file: File) {
  const previewTypes = [
    'image/png',
    'image/jpeg',
    'image/gif',
    'application/pdf',
    'text/plain',
    'text/markdown',
    'text/html',
  ]
  const encoded = encodeURIComponent(file.name) // 原始文件名带后缀
  return previewTypes.includes(file.type)
    ? 'inline'
    : `attachment; filename="${encoded}"`
}
export const uploadFile = ({
  file,
  onProgress,
}: {
  file: File
  onProgress?: (progressData: any) => void
}): Promise<{ url: string, data: any }> => {
  return new Promise((resolve, reject) => {
    const ext = getExt(file)
    const key = `${Date.now()}-${Math.random().toString(36).slice(2)}${ext}`
    console.log('key', key)

    cos.putObject(
      {
        Bucket,
        Region,
        Key: key,
        Body: file,
        ContentType: file.type || 'application/octet-stream',
        ContentDisposition: getDisposition(file),
        onProgress,
      },
      (err, data) => {
        if (err) {
          console.error('上传失败', err)
          reject(err)
        }
        else {
          const url = `https://${Bucket}.cos.${Region}.myqcloud.com/${key}`
          resolve({ url, data })
        }
      },
    )
  })
}

// https://third-1330359603.cos.ap-shanghai.myqcloud.com/1751989840753-rs7g9mq6jtp.jpg
// https://third-1330359603.cos.ap-shanghai.myqcloud.com/1751989722037-dqeo5bo4s07.jpg
