<script setup lang="ts">
import * as WX from 'jswx'
import { miscApi } from '~/api/misc'
import { preloadAssets } from '~/logic/preload-assets'

preloadAssets()

const userStore = useUserStore()
userStore.tryLogin()
  .then(async () => {
    const url = new URL(window.location.href)
    const currentPath = url.hash
    const homeUrl = '/pages/index/index'
    const loadingUrl = '/pages/loading/index'
    const shouldRedirect = !currentPath.includes(homeUrl) && !currentPath.includes(loadingUrl)
    if (shouldRedirect) {
      uni.reLaunch({
        url: homeUrl,
      })
    }

    const { data: { signature } } = await miscApi.getWxConfig()
    console.log('wxConfig', signature)
    WX.config({
      ...signature,
      debug: false,
      jsApiList: ['chooseImage', 'scanQRCode', 'previewImage'],
    })

    WX.ready(() => {
      console.log('wxConfig ready')
    })

    WX.error((error: any) => {
      console.error('wxConfig error', error)
    })
  })
  .catch(() => {
    uni.reLaunch({
      url: '/pages/login/index',
    })
  })
</script>

<style>
page {
  background-color: #f5f5f5;
  color: #1d2129;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  box-sizing: border-box;

  --wot-color-theme: $themeColor;
  --wot-navbar-title-font-weight: 400;
  --wot-navbar-title-font-size: 16px;
}

.wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
}

@font-face {
  font-family: 'jiangxizhuokai';
  src: url('./assets/fonts/jiangxizhuokai.ttf') format('truetype');
}

.font-hand {
  font-family: 'jiangxizhuokai';
  font-weight: 400;
  font-style: normal;
}

/* patch uni-toast style issues */
.uni-toast {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.uni-toast__content {
  font-size: 12px;
  margin-top: 15px;
}

.scrollbar::-webkit-scrollbar {
  width: 6px;
}

.scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}

uni-button:after {
  border: none !important;
}
</style>
