import type { CSSProperties } from 'vue'
import type { IMission } from '~/api/misc'

import { MissionTypeEnum } from '~/api/misc'
import atlantisIcon from '~/assets/images/stages/亚特兰蒂斯通道.png?url'
import penguinIcon from '~/assets/images/stages/企鹅馆.png?url'
import entranceIcon from '~/assets/images/stages/入口.png?url'
import jellyfishIcon from '~/assets/images/stages/水母馆.png?url'
import oceanIcon from '~/assets/images/stages/海洋嘉年华.png?url'
import belugaIcon from '~/assets/images/stages/白鲸馆.png?url'
import cuteBayIcon from '~/assets/images/stages/萌兽湾.png?url'
import theaterIcon from '~/assets/images/stages/鲸豚剧场.png?url'

import { safeJsonParse } from '~/utils'
import { isTaskStartModalVisibleRef } from './task'

// mission modal
export const isMissionModalVisibleRef = ref(false)
export const currentMissionIdRef = ref<string>()

export enum StateEnum {
  Entry = 'entry',
  Stage1 = '1',
  Stage2 = '2',
  Stage3 = '3',
  Stage4 = '4',
  Stage5 = '5',
  Stage6 = '6',
  Stage7 = '7',
}

export interface IStage {
  id: StateEnum
  name: string
  position: CSSProperties
  height: string
  icon: string
  onClick?: (stage: IStage) => unknown
  hasFinished?: boolean
  mission?: IMission
  missionInfo?: Record<string, any>
}

export const MissionComponentMap: Record<MissionTypeEnum, Component> = {
  [MissionTypeEnum.Question]: defineAsyncComponent(() => import('~/components/TheMissionModal/TheQuestionMission.vue')),
  [MissionTypeEnum.Photo]: defineAsyncComponent(() => import('~/components/TheMissionModal/ThePhotoMission.vue')),
  [MissionTypeEnum.Scan]: defineAsyncComponent(() => import('~/components/TheMissionModal/TheScanMission.vue')),
  [MissionTypeEnum.Click]: defineAsyncComponent(() => import('~/components/TheMissionModal/ThePhotoMission.vue')),
}

export const stageListRef = ref<IStage[]>([
  {
    id: StateEnum.Entry,
    name: '入口',
    position: { bottom: '-8%', left: '45%' },
    height: '11vh',
    icon: entranceIcon,
    hasFinished: true,
    onClick: () => {
      isTaskStartModalVisibleRef.value = true
    },
  },
  {
    id: StateEnum.Stage1,
    name: '企鹅馆',
    position: { bottom: '11%', left: '16%' },
    height: '11vh',
    icon: penguinIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage2,
    name: '白鲸馆',
    position: { bottom: '15%', right: '2%' },
    height: '11vh',
    icon: belugaIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage3,
    name: '亚特兰蒂斯通道',
    position: { bottom: '42%', left: '45%' },
    height: '11.2vh',
    icon: atlantisIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage4,
    name: '鲸豚剧场',
    position: { bottom: '40%', left: '3%' },
    height: '11vh',
    icon: theaterIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage5,
    name: '水母馆',
    position: { bottom: '70%', left: '25%' },
    height: '11vh',
    icon: jellyfishIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage6,
    name: '萌兽湾',
    position: { bottom: '68%', right: '7%' },
    height: '11vh',
    icon: cuteBayIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
  {
    id: StateEnum.Stage7,
    name: '海洋嘉年华',
    position: { bottom: '95%', right: '13%' },
    height: '11vh',
    icon: oceanIcon,
    onClick(stage) {
      isMissionModalVisibleRef.value = true
      currentMissionIdRef.value = stage.id
    },
  },
])

export const useStageList = (options?: { onSuccess?: () => void }) => {
  const { data: missionInfoRef, refetch, isLoading } = useQuery({
    queryKey: miscApi.getUserMissionListQueryKey,
    queryFn: miscApi.getUserMissionStatus,
    select: (data) => {
      const missionInfo = data?.data ?? {
        hasLottery: false,
        missionInfos: [],
      }
      stageListRef.value = stageListRef.value.map((stage) => {
        const mission = missionInfo.missionInfos.find(mission => mission.missionId === stage.id)
        return {
          ...stage,
          hasFinished: stage.hasFinished ?? mission?.isFinished ?? false,
          mission,
          missionInfo: safeJsonParse(mission?.missionInfo, undefined),
        }
      })
      options?.onSuccess?.()
      return missionInfo
    },
  })

  return {
    // stageListRef,
    // hasLotteryRef: computed(() => missionInfoRef.value?.hasLottery ?? false),
    // missionInfoListRef: computed(() => missionInfoRef.value?.missionInfos ?? []),
    isLoading,
    refetch,
    totalEnergy: computed(() => {
      return missionInfoRef.value?.missionInfos.filter(mission => mission.isFinished).length ?? 0
    }),
    // isAllMissionFinished: computed(() => {
    //   return missionInfoRef.value?.missionInfos.every(mission => mission.isFinished) ?? false
    // }),
  }
}
