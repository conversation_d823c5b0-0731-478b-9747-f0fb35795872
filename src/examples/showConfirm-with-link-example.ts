import { showConfirm } from '~/components/TheModal'

// 使用示例：在 showConfirm 中添加超链接

// 示例1：基本超链接用法
export const showConfirmWithLink = () => {
  const { close } = showConfirm({
    title: '我的能量石',
    message: `当前能量石: 100\n获得不同数量的能量石\n能够兑换不同的奖品、可前往指定区域领取`,
    confirmText: '好的',
    linkText: '查看详细规则',
    linkUrl: 'https://example.com/rules',
    onConfirm: () => {
      console.log('确认按钮被点击')
      close()
    },
    onLinkClick: () => {
      console.log('链接被点击，即将跳转')
      // 这里可以添加额外的逻辑，比如埋点统计
    }
  })
  
  return { close }
}

// 示例2：带有自定义链接处理的用法
export const showConfirmWithCustomLink = () => {
  const { close } = showConfirm({
    title: '活动说明',
    message: '参与活动即可获得丰厚奖品\n更多详情请查看活动规则',
    confirmText: '我知道了',
    linkText: '点击查看活动规则',
    linkUrl: 'https://activity.example.com/rules',
    onConfirm: () => {
      close()
    },
    onLinkClick: () => {
      // 自定义处理逻辑
      console.log('用户点击了活动规则链接')
      // 可以在这里添加埋点、统计等逻辑
    }
  })
  
  return { close }
}

// 示例3：仅有链接文本，通过 onLinkClick 自定义处理
export const showConfirmWithCustomHandler = () => {
  const { close } = showConfirm({
    title: '提示',
    message: '操作完成\n如需帮助请联系客服',
    confirmText: '确定',
    linkText: '联系客服',
    // 不设置 linkUrl，完全通过 onLinkClick 自定义处理
    onConfirm: () => {
      close()
    },
    onLinkClick: () => {
      // 自定义处理，比如打开客服聊天窗口
      console.log('打开客服聊天窗口')
      // 或者跳转到特定页面
      // uni.navigateTo({ url: '/pages/customer-service/index' })
    }
  })
  
  return { close }
}
