import { h } from 'vue'
import { TheModalWithSlots } from '~/components/TheModal'

// 使用示例：函数式调用 TheModal 并传入插槽内容

// 示例1：使用 content 属性传入简单内容
export const showSimpleModal = () => {
  const { close } = TheModalWithSlots({
    width: '80vw',
    content: () => [
      h('div', { class: 'text-center p-6' }, [
        h('h2', { class: 'text-xl font-bold mb-4' }, '确认操作'),
        h('p', { class: 'text-gray-600 mb-6' }, '您确定要执行此操作吗？'),
        h('div', { class: 'flex gap-4 justify-center' }, [
          h('button', {
            class: 'px-4 py-2 bg-gray-200 rounded',
            onClick: () => close()
          }, '取消'),
          h('button', {
            class: 'px-4 py-2 bg-blue-500 text-white rounded',
            onClick: () => {
              console.log('确认操作')
              close()
            }
          }, '确认')
        ])
      ])
    ],
    onClose: () => {
      console.log('模态框已关闭')
    }
  })
  
  return { close }
}

// 示例2：使用 slots 对象传入内容
export const showComplexModal = () => {
  const { close } = TheModalWithSlots({
    width: '90vw',
    gap: '2rem',
    slots: {
      default: () => [
        h('div', { class: 'bg-white rounded-lg p-6 w-full' }, [
          h('div', { class: 'flex items-center justify-between mb-4' }, [
            h('h3', { class: 'text-lg font-semibold' }, '用户信息'),
            h('span', { class: 'text-sm text-gray-500' }, '请填写完整信息')
          ]),
          h('form', { class: 'space-y-4' }, [
            h('div', {}, [
              h('label', { class: 'block text-sm font-medium mb-1' }, '姓名'),
              h('input', {
                type: 'text',
                class: 'w-full px-3 py-2 border rounded-md',
                placeholder: '请输入姓名'
              })
            ]),
            h('div', {}, [
              h('label', { class: 'block text-sm font-medium mb-1' }, '邮箱'),
              h('input', {
                type: 'email',
                class: 'w-full px-3 py-2 border rounded-md',
                placeholder: '请输入邮箱'
              })
            ]),
            h('div', { class: 'flex gap-3 pt-4' }, [
              h('button', {
                type: 'button',
                class: 'flex-1 px-4 py-2 border border-gray-300 rounded-md',
                onClick: () => close()
              }, '取消'),
              h('button', {
                type: 'submit',
                class: 'flex-1 px-4 py-2 bg-blue-600 text-white rounded-md',
                onClick: (e: Event) => {
                  e.preventDefault()
                  console.log('提交表单')
                  close()
                }
              }, '提交')
            ])
          ])
        ])
      ]
    },
    onSuccess: (result) => {
      console.log('操作成功:', result)
    },
    onError: (error) => {
      console.error('操作失败:', error)
    }
  })
  
  return { close }
}

// 示例3：传入 Vue 组件作为插槽内容
export const showComponentModal = (component: any, props: any = {}) => {
  const { close } = TheModalWithSlots({
    width: '95vw',
    content: () => h(component, {
      ...props,
      onClose: close,
      onConfirm: (data: any) => {
        console.log('组件确认:', data)
        close()
      }
    }),
    onClose: () => {
      console.log('组件模态框关闭')
    }
  })
  
  return { close }
}

// 示例4：动态内容模态框
export const showDynamicModal = (title: string, message: string, actions: Array<{
  text: string
  type?: 'primary' | 'secondary' | 'danger'
  onClick: () => void
}>) => {
  const { close } = TheModalWithSlots({
    content: () => [
      h('div', { class: 'bg-white rounded-lg p-6 max-w-sm w-full' }, [
        h('h3', { class: 'text-lg font-semibold mb-3' }, title),
        h('p', { class: 'text-gray-600 mb-6' }, message),
        h('div', { class: 'flex gap-2 justify-end' }, 
          actions.map(action => 
            h('button', {
              class: [
                'px-4 py-2 rounded-md text-sm font-medium',
                action.type === 'primary' ? 'bg-blue-600 text-white' :
                action.type === 'danger' ? 'bg-red-600 text-white' :
                'bg-gray-200 text-gray-800'
              ],
              onClick: () => {
                action.onClick()
                close()
              }
            }, action.text)
          )
        )
      ])
    ]
  })
  
  return { close }
}

// 使用方法示例：
/*
// 在组件中使用
const handleShowModal = () => {
  showSimpleModal()
}

const handleShowComplexModal = () => {
  showComplexModal()
}

const handleShowDynamicModal = () => {
  showDynamicModal('删除确认', '确定要删除这个项目吗？此操作不可撤销。', [
    { text: '取消', type: 'secondary', onClick: () => console.log('取消') },
    { text: '删除', type: 'danger', onClick: () => console.log('删除') }
  ])
}
*/
