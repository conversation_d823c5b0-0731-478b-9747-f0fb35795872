<script setup lang="ts">
import { truncate } from 'lodash-es'
import bg from '~/assets/images/bg.png?url'
import heroTextIcon from '~/assets/images/文字.png?url'

const userStore = useUserStore()

const isLoading = ref(false)
const loginError = ref('')
const showRetry = ref(false)

const login = async () => {
  loginError.value = ''
  showRetry.value = false
  isLoading.value = true
  const [err] = await to(userStore.tryLogin())
  isLoading.value = false
  if (err) {
    // @ts-expect-error ignore
    loginError.value = err.message || err?.data?.msg || '登录失败，请重试'
    showRetry.value = true
    isLoading.value = false
    return
  }

  await sleep(600)

  uni.reLaunch({
    url: '/pages/index/index',
  })
}

const retry = () => {
  login()
}

login()
</script>

<template>
  <div
    class="min-h-screen w-full flex flex-col items-center relative overflow-hidden bg-image justify-center"
    :style="{ backgroundImage: `url(${bg})` }"
  >
    <!-- Logo Section -->
    <div class="mb-8 flex flex-col items-center">
      <img
        :src="heroTextIcon"
        alt="极地探险家"
        class="h-auto w-51.2vw max-w-220px"
      >
    </div>

    <!-- Main Content -->
    <div class=" z-2 w-80vw max-w-96 bg-white/90 backdrop-blur rounded-3xl p-8 flex flex-col items-center shadow-xl text-center">
      <!-- Login Status -->
      <div class="mb-6 w-full">
        <div v-if="isLoading" class="flex flex-col items-center">
          <div class="i-svg-spinners-ring-resize w-10 h-10 text-blue-500 mb-3 animate-spin" />
          <p class="text-base text-gray-500">
            正在登录中...
          </p>
        </div>
        <div v-else-if="loginError && showRetry" class="flex flex-col items-center">
          <div class="text-4xl mb-2">
            ⚠️
          </div>
          <p class="text-xs text-red-600 mb-6 mt-2 text-pretty">
            {{ truncate(loginError, { length: 200, omission: '...' }) }}
          </p>
          <button class="text-base px-6 py-2 rounded bg-gradient-to-r from-blue-500 to-blue-700 text-white shadow hover:scale-105 transition" @click="retry">
            重新登录
          </button>
        </div>
        <div v-else class="flex flex-col items-center">
          <div class="text-4xl mb-2">
            🌊
          </div>

          <p class="text-sm text-#38769E font-medium">
            准备进入...
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div class="border-t border-gray-200 pt-4 w-full">
        <p class="text-xs text-gray-400">
          探索无尽的海底宝藏
        </p>
      </div>
    </div>
  </div>
</template>
