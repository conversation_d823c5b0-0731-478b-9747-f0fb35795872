<script setup lang="ts">
import { ref } from 'vue'

const title = ref('拍照Demo')
const imageUrl = ref('')
const isLoading = ref(false)
const isSaving = ref(false)

// 拍照功能
const takePhoto = () => {
  console.log('开始拍照')

  // 检查是否在小程序环境中
  if (typeof uni === 'undefined') {
    console.error('uni对象未定义，请在小程序中使用此功能')
    return
  }

  isLoading.value = true

  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['camera'], // 只允许拍照，不允许从相册选择
    success(res) {
      try {
        console.log('拍照成功:', res)
        const tempFilePaths = res.tempFilePaths
        if (tempFilePaths && tempFilePaths.length > 0) {
          imageUrl.value = tempFilePaths[0]
          uni.showToast({
            title: '拍照成功',
            icon: 'success',
          })
        }
        else {
          throw new Error('未获取到图片路径')
        }
      }
      catch (error) {
        console.error('处理拍照结果失败:', error)
        uni.showToast({
          title: '拍照失败，请重试',
          icon: 'none',
        })
      }
    },
    fail(err) {
      console.error('拍照失败:', err)
      let errorMsg = '拍照失败'

      // 根据不同错误类型给出不同提示
      if (err.errMsg) {
        if (err.errMsg.includes('cancel')) {
          errorMsg = '用户取消拍照'
        }
        else if (err.errMsg.includes('permission')) {
          errorMsg = '请授权相机权限'
        }
        else if (err.errMsg.includes('system')) {
          errorMsg = '系统错误，请重试'
        }
      }

      uni.showToast({
        title: errorMsg,
        icon: 'none',
      })
    },
    complete() {
      isLoading.value = false
    },
  })
}

// 声明全局类型
declare global {
  interface Window {
    __wxjs_environment?: string
    wx?: {
      miniProgram: {
        postMessage: (options: { data: any }) => void
        navigateBack: () => void
      }
    }
  }
}

// 检查是否在小程序webview环境中
const isInMiniProgram = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram') || (window as any).__wxjs_environment === 'miniprogram'
}

// 显示消息提示
const showMessage = (message: string) => {
  // 优先使用console.log，避免alert的ESLint警告
  console.log(`[用户提示] ${message}`)
  // 在实际项目中，可以使用更好的提示组件
  // 这里暂时使用console.log代替alert
}

// 向小程序发送消息
const postMessageToMiniProgram = (data: any) => {
  try {
    console.log('发送保存图片消息到小程序:', (uni as any)?.webView?.postMessage)
    // 使用 uni.postMessage 发送消息到小程序
    // 需要确保在 webview 环境中调用
    if (typeof uni !== 'undefined' && (uni as any)?.webView?.postMessage) {
      (uni as any).webView.postMessage({
        data: {
          action: 'saveImage',
          ...data,
        },
      })
      console.log('消息已发送到小程序')
    }
    else {
      console.warn('uni.postMessage 不可用，可能不在 webview 环境中')
    }
  }
  catch (error) {
    console.error('发送消息到小程序失败:', error)
  }
}

// 保存图片到本地（通过小程序）
const saveImage = () => {
  if (!imageUrl.value) {
    showMessage('请先拍照')
    return
  }

  isSaving.value = true

  try {
    // 将图片信息发送给小程序
    const messageData = {
      type: 'saveImage',
      imageUrl: imageUrl.value,
      timestamp: Date.now(),
    }

    console.log('发送保存图片消息到小程序:', messageData)

    if (isInMiniProgram()) {
      postMessageToMiniProgram(messageData)
      showMessage('图片信息已发送到小程序，请在小程序中确认保存')
    }
    else {
      // 非小程序环境的处理
      console.warn('当前不在小程序环境中，无法保存图片')
      showMessage('请在小程序中使用此功能')
    }
  }
  catch (error) {
    console.error('保存图片失败:', error)
    showMessage('保存失败，请重试')
  }
  finally {
    isSaving.value = false
  }
}

// 重新拍照
const retakePhoto = () => {
  imageUrl.value = ''
  takePhoto()
}
</script>

<template>
  <view class="content">
    <!-- 标题区域 -->
    <view class="header">
      <text class="title">
        {{ title }}
      </text>
    </view>

    <!-- 图片展示区域 -->
    <view class="image-container">
      <image v-if="imageUrl" :src="imageUrl" class="photo-image" mode="aspectFit" />
      <view v-else class="placeholder">
        <text class="placeholder-text">
          点击下方按钮开始拍照
        </text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-area">
      <button v-if="!imageUrl" :disabled="isLoading" class="photo-btn" :class="{ loading: isLoading }"
        @click="takePhoto">
        {{ isLoading ? '拍照中...' : '📷 拍照' }}
      </button>

      <view v-else class="action-buttons">
        <button :disabled="isLoading" class="retake-btn" @click="retakePhoto">
          🔄 重新拍照
        </button>
        <button :disabled="isSaving" class="save-btn" :class="{ saving: isSaving }" @click="saveImage">
          {{ isSaving ? '保存中...' : '💾 保存到相册' }}
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  min-height: 600rpx;
}

.photo-image {
  max-width: 100%;
  max-height: 600rpx;
  border-radius: 16rpx;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  border: 4rpx dashed #ddd;
  border-radius: 16rpx;
  margin: 40rpx;
}

.placeholder-text {
  font-size: 32rpx;
  color: #999;
}

.button-area {
  padding: 40rpx 0;
}

.photo-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.photo-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.photo-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.retake-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.retake-btn {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  color: #2d3436;
  box-shadow: 0 6rpx 20rpx rgba(255, 234, 167, 0.4);
}

.save-btn {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.4);
}

.retake-btn:active,
.save-btn:active {
  transform: translateY(2rpx);
}

.save-btn.saving {
  background: #ccc;
  box-shadow: none;
}

.retake-btn:disabled,
.save-btn:disabled {
  opacity: 0.6;
  transform: none;
}
</style>