<script setup lang="ts">
import { first } from 'lodash-es'
import { ref } from 'vue'
import { uploadFile } from '~/api/uploadFile'

const title = ref('拍照Demo')
const imageUrl = ref('')
let tempFiles: File[] = []
const isLoading = ref(false)
const isSaving = ref(false)

// 拍照功能
const takePhoto = () => {
  console.log('开始拍照')

  // 检查是否在小程序环境中
  if (typeof uni === 'undefined') {
    console.error('uni对象未定义，请在小程序中使用此功能')
    return
  }

  isLoading.value = true

  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['camera'], // 只允许拍照，不允许从相册选择
    success(res) {
      try {
        console.log('拍照成功:', res)
        const tempFilePaths = res.tempFilePaths
        if (tempFilePaths && tempFilePaths.length > 0) {
          imageUrl.value = tempFilePaths[0]
          tempFiles = res.tempFiles as File[]
          uni.showToast({
            title: '拍照成功',
            icon: 'success',
          })
        }
        else {
          throw new Error('未获取到图片路径')
        }
      }
      catch (error) {
        console.error('处理拍照结果失败:', error)
        uni.showToast({
          title: '拍照失败，请重试',
          icon: 'none',
        })
      }
    },
    fail(err) {
      console.error('拍照失败:', err)
      let errorMsg = '拍照失败'

      // 根据不同错误类型给出不同提示
      if (err.errMsg) {
        if (err.errMsg.includes('cancel')) {
          errorMsg = '用户取消拍照'
        }
        else if (err.errMsg.includes('permission')) {
          errorMsg = '请授权相机权限'
        }
        else if (err.errMsg.includes('system')) {
          errorMsg = '系统错误，请重试'
        }
      }

      uni.showToast({
        title: errorMsg,
        icon: 'none',
      })
    },
    complete() {
      isLoading.value = false
    },
  })
}

// 上传图片
const uploadImage = () => {
  try {
    const tempFile = first(tempFiles)
    console.log('上传图片到小程序:', tempFiles)
    // data.imageUrl = 'blob:http://localhost:5173/84a3003e-c31a-4692-97c9-dc6b5d6639fe' // 设置图片URL
    if (tempFile) {
      uploadFile({
        file: tempFile,
        onProgress: (progressEvent) => {
          console.log('上传进度:', progressEvent)
        },
      }).then((res) => {
        console.log('上传成功:', res)
      })
    }
  }
  catch (error) {
    console.error('发送消息到小程序失败:', error)
  }
}

// 重新拍照
const retakePhoto = () => {
  imageUrl.value = ''
  takePhoto()
}
</script>

<template>
  <view class="content">
    <!-- 标题区域 -->
    <view class="header">
      <text class="title">
        {{ title }}
      </text>
    </view>

    <!-- 图片展示区域 -->
    <view class="image-container">
      <image v-if="imageUrl" :src="imageUrl" class="photo-image" mode="aspectFit" />
      <view v-else class="placeholder">
        <text class="placeholder-text">
          点击下方按钮开始拍照
        </text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-area">
      <button v-if="!imageUrl" :disabled="isLoading" class="photo-btn" :class="{ loading: isLoading }"
        @click="takePhoto">
        {{ isLoading ? '拍照中...' : '📷 拍照' }}
      </button>

      <view v-else class="action-buttons">
        <button :disabled="isLoading" class="retake-btn" @click="retakePhoto">
          🔄 重新拍照
        </button>
        <button :disabled="isSaving" class="save-btn" :class="{ saving: isSaving }" @click="uploadImage">
          {{ isSaving ? '保存中...' : '💾 上传' }}
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
.content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  min-height: 600rpx;
}

.photo-image {
  max-width: 100%;
  max-height: 600rpx;
  border-radius: 16rpx;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  border: 4rpx dashed #ddd;
  border-radius: 16rpx;
  margin: 40rpx;
}

.placeholder-text {
  font-size: 32rpx;
  color: #999;
}

.button-area {
  padding: 40rpx 0;
}

.photo-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.photo-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.photo-btn.loading {
  background: #ccc;
  box-shadow: none;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.retake-btn,
.save-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.retake-btn {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  color: #2d3436;
  box-shadow: 0 6rpx 20rpx rgba(255, 234, 167, 0.4);
}

.save-btn {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.4);
}

.retake-btn:active,
.save-btn:active {
  transform: translateY(2rpx);
}

.save-btn.saving {
  background: #ccc;
  box-shadow: none;
}

.retake-btn:disabled,
.save-btn:disabled {
  opacity: 0.6;
  transform: none;
}
</style>