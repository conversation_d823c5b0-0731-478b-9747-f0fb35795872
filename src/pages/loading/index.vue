<script lang="ts" setup>
import { onMounted, ref } from 'vue'

// 响应式数据
const loadingText = ref('正在处理')
const progress = ref(0)
const showProgress = ref(true)
const isCompleted = ref(false)

// 定时器引用
let textAnimationTimer: number | null = null

// 处理文本动画
const loadingTexts = ['正在处理', '正在处理.', '正在处理..', '正在处理...']
let textIndex = 0

// 文本动画效果
const animateText = () => {
  textAnimationTimer = setInterval(() => {
    if (!isCompleted.value) {
      textIndex = (textIndex + 1) % loadingTexts.length
      loadingText.value = loadingTexts[textIndex]
    }
  }, 500)
}

// 停止文本动画
const stopTextAnimation = () => {
  if (textAnimationTimer) {
    clearInterval(textAnimationTimer)
    textAnimationTimer = null
  }
}

// 假进度条动画 - 2秒内完成
const animateProgress = () => {
  const duration = 2000 // 2秒
  const startTime = Date.now()

  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    const progressPercent = Math.min((elapsed / duration) * 100, 100)

    // 添加一些随机性让进度看起来更真实
    const randomOffset = Math.random() * 5 - 2.5 // -2.5 到 2.5 的随机偏移
    progress.value = Math.min(Math.max(progressPercent + randomOffset, 0), 100)

    if (elapsed < duration) {
      requestAnimationFrame(updateProgress)
    }
    else {
      // 确保最终进度为100%
      progress.value = 100
      // 延迟300ms显示完成状态
      setTimeout(() => {
        isCompleted.value = true
        stopTextAnimation()
        loadingText.value = '处理完成'
      }, 300)
    }
  }

  requestAnimationFrame(updateProgress)
}

// 按钮点击事件
const goBack = () => {
  // 这里可以根据您的路由配置进行跳转
  // 例如：router.push('/') 或 window.history.back()
  // window.history.back()
  uni.redirectTo({
    url: '/pages/index/index',
  })
}

onMounted(() => {
  animateText()
  animateProgress()
})
</script>

<template>
  <div class="loading-container">
    <div class="loading-content">
      <!-- 主要处理动画 -->
      <div class="spinner-wrapper">
        <div v-if="!isCompleted" class="spinner">
          <div class="spinner-ring" />
          <div class="spinner-ring" />
          <div class="spinner-ring" />
        </div>
        <!-- 完成状态图标 -->
        <div v-else class="success-icon">
          <svg class="checkmark" viewBox="0 0 52 52">
            <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none" />
            <path class="checkmark-check" fill="none" d="m14.1 27.2l7.1 7.2 16.7-16.8" />
          </svg>
        </div>
      </div>

      <!-- 处理文本 -->
      <div class="loading-text">
        <h2 class="loading-title" :class="{ completed: isCompleted }">
          {{ loadingText }}
        </h2>
        <p v-if="!isCompleted" class="loading-subtitle">
          请稍候...
        </p>
        <p v-else class="loading-subtitle success-text">
          准备就绪
        </p>
      </div>

      <!-- 进度条 -->
      <div v-if="showProgress" class="progress-bar">
        <div class="progress-fill" :style="{ width: `${progress}%` }" :class="{ completed: isCompleted }" />
      </div>

      <!-- 进度百分比 -->
      <div v-if="showProgress" class="progress-text">
        {{ Math.round(progress) }}%
      </div>

      <!-- 返回按钮 -->
      <div v-if="isCompleted" class="action-buttons">
        <button class="back-button" @click="goBack">
          <svg class="button-icon" viewBox="0 0 24 24" fill="none">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          返回首页
        </button>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="floating-circle circle-1" />
      <div class="floating-circle circle-2" />
      <div class="floating-circle circle-3" />
    </div>
  </div>
</template>

<style scoped>
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.loading-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

/* 主要处理动画 */
.spinner-wrapper {
  margin-bottom: 80rpx;
}

.spinner {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 6rpx solid transparent;
  border-top: 6rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
  border-top-color: rgba(255, 255, 255, 0.9);
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.5s;
  border-top-color: rgba(255, 255, 255, 0.7);
  width: 90%;
  height: 90%;
  top: 5%;
  left: 5%;
}

.spinner-ring:nth-child(3) {
  animation-delay: -1s;
  border-top-color: rgba(255, 255, 255, 0.5);
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 成功图标样式 */
.success-icon {
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto;
  animation: successPop 0.6s ease-out;
}

.checkmark {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #00e676;
  stroke-miterlimit: 10;
  box-shadow: inset 0rpx 0rpx 0rpx #00e676;
  filter: drop-shadow(0 0 20rpx rgba(0, 230, 118, 0.5));
}

.checkmark-circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: rgba(0, 230, 118, 0.4);
  fill: rgba(0, 230, 118, 0.1);
  animation: checkmarkCircle 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark-check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  stroke: #00e676;
  stroke-width: 3;
  animation: checkmarkCheck 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.3s forwards;
  filter: drop-shadow(0 0 10rpx rgba(0, 230, 118, 0.3));
}

@keyframes successPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmarkCircle {
  0% {
    stroke-dashoffset: 166;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes checkmarkCheck {
  0% {
    stroke-dashoffset: 48;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

/* 处理文本样式 */
.loading-text {
  color: #ffffff;
  margin-bottom: 100rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.loading-title {
  font-size: 72rpx;
  font-weight: 500;
  margin: 0 0 32rpx 0;
  letter-spacing: 4rpx;
  animation: fadeInOut 2s ease-in-out infinite;
  transition: all 0.4s ease;
  color: #ffffff;
}

.loading-title.completed {
  animation: none;
  color: #00e676;
  transform: scale(1.05);
  text-shadow: 0 0 40rpx rgba(0, 230, 118, 0.5);
}

.loading-subtitle {
  font-size: 40rpx;
  opacity: 0.85;
  margin: 0;
  font-weight: 400;
  transition: all 0.4s ease;
  color: rgba(255, 255, 255, 0.9);
}

.success-text {
  color: #00e676 !important;
  opacity: 1 !important;
  text-shadow: 0 0 30rpx rgba(0, 230, 118, 0.4);
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 进度条样式 */
.progress-bar {
  width: 560rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  overflow: hidden;
  margin: 0 auto;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #64b5f6, #42a5f5, #2196f3);
  border-radius: 16rpx;
  transition:
    width 0.3s ease,
    background 0.4s ease,
    box-shadow 0.4s ease;
  box-shadow: 0 0 30rpx rgba(33, 150, 243, 0.4);
}

.progress-fill.completed {
  background: linear-gradient(90deg, #00e676, #00c853, #4caf50);
  box-shadow: 0 0 40rpx rgba(0, 230, 118, 0.6);
}

/* 进度文本样式 */
.progress-text {
  color: #ffffff;
  font-size: 36rpx;
  margin-top: 32rpx;
  font-weight: 500;
  opacity: 0.95;
  transition: all 0.3s ease;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 按钮样式 */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 100rpx;
  animation: slideUp 0.8s ease-out 0.5s both;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 48rpx;
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 50rpx; /* 胶囊形状 - 高度的一半以上 */
  font-size: 32rpx;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  font-family: inherit;
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
}

/* 按钮悬停效果 */
.back-button:hover {
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.15);
}

/* 按钮点击效果 */
.back-button:active {
  transform: translateY(0rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 255, 255, 0.1);
}

/* 按钮内部光效（可选） */
.back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.back-button:hover::before {
  left: 100%;
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  stroke-width: 2;
  transition: transform 0.3s ease;
}

.back-button:hover .button-icon {
  transform: translateX(-4rpx);
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120rpx;
  height: 120rpx;
  top: 60%;
  right: 15%;
  animation-delay: -2s;
}

.circle-3 {
  width: 160rpx;
  height: 160rpx;
  bottom: 20%;
  left: 20%;
  animation-delay: -4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0rpx) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-40rpx) rotate(180deg);
    opacity: 0.6;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .spinner {
    width: 120rpx;
    height: 120rpx;
  }

  .success-icon {
    width: 120rpx;
    height: 120rpx;
  }

  .checkmark {
    width: 120rpx;
    height: 120rpx;
  }

  .loading-title {
    font-size: 56rpx;
  }

  .loading-subtitle {
    font-size: 36rpx;
  }

  .progress-bar {
    width: 440rpx;
  }

  .progress-text {
    font-size: 32rpx;
  }

  .action-buttons {
    margin-top: 80rpx;
  }

  .back-button {
    padding: 28rpx 44rpx;
    font-size: 30rpx;
    border-radius: 44rpx; /* 调整移动端胶囊圆角 */
  }

  .button-icon {
    width: 24rpx;
    height: 24rpx;
  }

  .floating-circle {
    display: none; /* 在小屏幕上隐藏装饰元素 */
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .loading-container {
    background: linear-gradient(135deg, #0f1419 0%, #1a202c 50%, #2d3748 100%);
  }

  .back-button {
    color: rgba(226, 232, 240, 0.9);
    border-color: rgba(226, 232, 240, 0.3);
  }

  .back-button:hover {
    color: #ffffff;
    border-color: rgba(226, 232, 240, 0.5);
    background: rgba(226, 232, 240, 0.1);
    box-shadow: 0 8rpx 32rpx rgba(226, 232, 240, 0.1);
  }
}
</style>
