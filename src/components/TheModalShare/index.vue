<script lang="ts" setup>
import { AnimatePresence, motion } from 'motion-v'
import { showConfirm } from '~/components/TheModal'
import { isShareModalVisibleRef } from '~/logic/task'

interface Props {
  title?: string
  description?: string
}

withDefaults(defineProps<Props>(), {
  title: '邀请好友购票赢好礼',
  description: '点击小程序右上角"..."按钮\n邀请好友购票赢好礼！',
})

const emit = defineEmits<{
  close: []
}>()

// 内部控制显示状态，用于处理动画时序
const internalVisibleRef = ref(false)

let timer: number | undefined

// 监听外部 visible 变化
watch(isShareModalVisibleRef, (newVal) => {
  if (newVal) {
    miscApi.shareFriend()
    internalVisibleRef.value = true
    if (timer) {
      clearTimeout(timer)
      timer = undefined
    }
    timer = setTimeout(() => {
      isShareModalVisibleRef.value = false
      const { close } = showConfirm({
        title: '分享成功',
        type: 'success',
        message: '感谢您的邀请, 请凭分享截图至线下指定地点领取, 兑换好礼。',
        confirmText: '我知道了',
        linkText: '点击查看活动规则',
        linkUrl: 'https://baidu.com',
        onConfirm: () => {
          isShareModalVisibleRef.value = false
          close()
        },
      })
      timer = undefined
    }, 3_000)
  }
  else {
    if (timer) {
      clearTimeout(timer)
      timer = undefined
    }
    // 延迟隐藏，让退出动画完成
    setTimeout(() => {
      internalVisibleRef.value = false
    }, 50) // 给动画一点时间开始
  }
}, { immediate: true })

const handleClose = () => {
  isShareModalVisibleRef.value = false
  emit('close')
}
</script>

<template>
  <AnimatePresence>
    <!-- 背景遮罩和内容区域包装在一个容器中，确保同步退出 -->
    <motion.div
      v-if="internalVisibleRef"
      class="fixed inset-0 z-50"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :transition="{ duration: 0.3, ease: 'easeInOut' }"
    >
      <!-- 背景遮罩 -->
      <div
        class="absolute inset-0 bg-black/50"
        @click="handleClose"
      />

      <!-- 分享指导内容 -->
      <motion.div
        class="absolute top-0 right-0 p-[20rpx]"
        :initial="{ scale: 0.8, x: 20, y: -20 }"
        :animate="{ scale: 1, x: 0, y: 0 }"
        :exit="{ scale: 0.8, x: 20, y: -20 }"
        :transition="{ duration: 0.3, type: 'spring', stiffness: 300, damping: 25 }"
      >
        <!-- 指导卡片 -->
        <div class="relative">
          <!-- 手机界面模拟 -->
          <div class="bg-white rounded-2xl shadow-2xl p-4 w-72 relative">
            <!-- 模拟手机顶部状态栏 -->
            <div v-if="false" class="flex items-center justify-between mb-3 px-2">
              <div class="flex items-center gap-1">
                <div class="w-1 h-1 bg-gray-400 rounded-full" />
                <div class="w-1 h-1 bg-gray-400 rounded-full" />
                <div class="w-1 h-1 bg-gray-400 rounded-full" />
              </div>
              <div class="text-xs text-gray-600 font-medium">
                9:41
              </div>
              <div class="flex items-center gap-1">
                <div class="w-3 h-2 border border-gray-400 rounded-sm" />
                <div class="w-1 h-1 bg-gray-400 rounded-full" />
              </div>
            </div>

            <!-- 模拟导航栏 -->
            <div class="flex items-center justify-between mb-4 px-2">
              <div class="text-sm text-gray-600">
                {{ title }}
              </div>
              <!-- 三点按钮 -->
              <motion.div
                class="flex gap-0.5 cursor-pointer p-1"
                :animate="{ scale: [1, 1.2, 1] }"
                :transition="{ duration: 1.5, repeat: Infinity, repeatDelay: 2 }"
              >
                <div class="w-1 h-1 bg-gray-800 rounded-full" />
                <div class="w-1 h-1 bg-gray-800 rounded-full" />
                <div class="w-1 h-1 bg-gray-800 rounded-full" />
              </motion.div>
            </div>

            <!-- 指导文字 -->
            <div class="text-center py-6">
              <!--              <div class="text-lg font-medium text-gray-800 mb-2"> -->
              <!--                {{ title }} -->
              <!--              </div> -->
              <div class="text-sm text-gray-600 leading-relaxed whitespace-pre-line">
                {{ description }}
              </div>
            </div>

            <!-- 关闭按钮 -->
            <!--            <div class="flex justify-center"> -->
            <!--              <motion.button -->
            <!--                class="px-6 py-2 bg-white border border-gray-300 rounded-full text-sm text-gray-600 hover:bg-gray-50 transition-colors" -->
            <!--                :while-hover="{ scale: 1.05 }" -->
            <!--                :while-press="{ scale: 0.95 }" -->
            <!--                @click="handleClose" -->
            <!--              > -->
            <!--                我知道了 -->
            <!--              </motion.button> -->
            <!--            </div> -->
          </div>

          <!-- 指向手指动画 -->
          <motion.div
            class="absolute top-[50rpx] right-[10rpx] text-2xl"
            :initial="{ opacity: 0, rotate: -45 }"
            :animate="isShareModalVisibleRef ? {
              opacity: 1,
              rotate: [-45, -35, -45],
              x: [0, -3, 0],
              y: [0, 3, 0],
            } : { opacity: 0, rotate: -45, x: 0, y: 0 }"
            :exit="{ opacity: 0, rotate: -45, x: 0, y: 0 }"
            :transition="{
              opacity: { delay: isShareModalVisibleRef ? 0.5 : 0, duration: 0.3 },
              rotate: { duration: isShareModalVisibleRef ? 1.5 : 0.3, repeat: isShareModalVisibleRef ? Infinity : 0, repeatDelay: isShareModalVisibleRef ? 1 : 0 },
              x: { duration: isShareModalVisibleRef ? 1.5 : 0.3, repeat: isShareModalVisibleRef ? Infinity : 0, repeatDelay: isShareModalVisibleRef ? 1 : 0 },
              y: { duration: isShareModalVisibleRef ? 1.5 : 0.3, repeat: isShareModalVisibleRef ? Infinity : 0, repeatDelay: isShareModalVisibleRef ? 1 : 0 },
            }"
          >
            👆
          </motion.div>
        </div>
      </motion.div>
    </motion.div>
  </AnimatePresence>
</template>

<style scoped>
/* 确保组件在小程序中正常显示 */
.fixed {
  position: fixed;
}
</style>
