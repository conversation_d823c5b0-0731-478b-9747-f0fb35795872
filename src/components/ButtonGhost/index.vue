<script lang="ts" setup>
import { motion } from 'motion-v'

// 定义 props
interface Props {
  enableSlideUpAnimation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enableSlideUpAnimation: false,
})
</script>

<template>
  <motion.button
    :while-hover="{ scale: 1.05 }"
    :while-press="{ scale: 0.95 }"
    class="ghost-button"
    :transition="props.enableSlideUpAnimation ? { y: { delay: 1.05 }, opacity: { delay: 1.05 }, duration: 0.3 } : {}"
    :initial="props.enableSlideUpAnimation ? { y: '2rem', opacity: 0 } : {}"
    :while-in-view="props.enableSlideUpAnimation ? { y: 0, opacity: 1 } : {}"
  >
    <span class="ghost-button-text">
      <slot />
    </span>
  </motion.button>
</template>

<style lang="scss" scoped>
.ghost-button {
  width: 100%;
  padding: 24rpx 48rpx;
  background: transparent;
  color: white;
  border: 4rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 50rpx; /* 胶囊形状 */
  font-size: 32rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  font-family: inherit;
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 88rpx;
}

/* 按钮悬停效果 */
.ghost-button:hover {
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 255, 255, 0.15);
}

/* 按钮按下效果 */
.ghost-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.05);
}

/* 按钮禁用状态 */
.ghost-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: transparent;
  border-color: rgba(255, 255, 255, 0.3);
}

.ghost-button:disabled:hover {
  transform: none;
  background: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

.ghost-button-text {
  font-size: 3.5vw;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
</style>
