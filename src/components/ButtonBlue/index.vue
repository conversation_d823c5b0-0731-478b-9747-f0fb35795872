<script lang="ts" setup>
import { motion } from 'motion-v'
import buttonIconUrl from '~/assets/images/规则.png?url'

// 定义 props
interface Props {
  enableSlideUpAnimation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enableSlideUpAnimation: false,
})
</script>

<template>
  <motion.div
    :while-hover="{ scale: 1.1 }"
    :while-press="{ scale: 0.9 }"
    class="w-38vw h-auto relative select-none"
    :transition="props.enableSlideUpAnimation ? { y: { delay: 1.05 }, opacity: { delay: 1.05 }, duration: 0.3 } : {}"
    :initial="props.enableSlideUpAnimation ? { y: '2rem', opacity: 0 } : {}"
    :while-in-view="props.enableSlideUpAnimation ? { y: 0, opacity: 1 } : {}"
  >
    <img :src="buttonIconUrl" alt="按钮" class="w-full h-auto select-none no-callout">
    <div class="absolute inset-0 flex items-center justify-center">
      <span class="font-hand text-white text-stroke-button">
        <slot />
      </span>
    </div>
  </motion.div>
</template>

<style type="scss" scoped>
/* 按钮文字描边效果 */
.text-stroke-button {
  font-size: 3.5vw;
  font-weight: 600;
  text-shadow:
    1px -1px 0 #2a63b4,
    -1px 1px 0 #2a63b4,
    1px 1px 0 #2a63b4,
    -2px 0 0 #2a63b4,
    2px 0 0 #2a63b4,
    0 -2px 0 #2a63b4,
    0 2px 0 #2a63b4;
  letter-spacing: 0.5px;
}
</style>
