<script setup lang="ts">
import { klona } from 'klona'
import { motion } from 'motion-v'
import { btnCompleteIconUrl, btnNextIconUrl, done02IconUrl } from '~/logic/preload-assets'
import { currentMissionIdRef, isMissionModalVisibleRef, stageListRef } from '~/logic/stage'
import { isTakePhotoModalVisibleRef, isTaskCompleteModalVisibleRef } from '~/logic/task'

const currentMissionIndexRef = computed(() => {
  return klona(stageListRef.value).findIndex(stage => stage.id === currentMissionIdRef.value)
})

const isHasNextTaskRef = computed(() => {
  const idx = currentMissionIndexRef.value
  return idx !== -1 && idx !== stageListRef.value.length - 1
})

const handleAllTaskComplete = () => {
  isTaskCompleteModalVisibleRef.value = false
  isMissionModalVisibleRef.value = false
  isTakePhotoModalVisibleRef.value = true
}

const handleGoNext = () => {
  isTaskCompleteModalVisibleRef.value = false

  const idx = currentMissionIndexRef.value

  if (isHasNextTaskRef.value) {
    currentMissionIdRef.value = stageListRef.value[idx + 1].id
    isMissionModalVisibleRef.value = true
    return
  }

  handleAllTaskComplete()
}

const handleComplete = () => {
  isTaskCompleteModalVisibleRef.value = false
  isMissionModalVisibleRef.value = false

  if (isHasNextTaskRef.value) {
    return
  }

  handleAllTaskComplete()
}
</script>

<template>
  <TheModal v-model:visible="isTaskCompleteModalVisibleRef" width="96vw">
    <div
      class="w-full bg-image aspect-565/621 relative"
      :style="{
        backgroundImage: `url(${done02IconUrl})`,
      }"
    >
      <div class="flex items-center justify-around gap-2 absolute bottom-28vw px-10vw w-full">
        <p class="font-hand text-white text-stroke-button">
          恭喜您完成第 {{ currentMissionIdRef }} 关, 获得一颗能量石
        </p>
      </div>
      <div v-if="currentMissionIdRef === '4' || currentMissionIdRef === '7'" class="flex items-center justify-around gap-2 absolute bottom-18vw px-10vw w-full">
        <p class="font-hand text-white text-stroke-button">
          您已经集齐 {{ currentMissionIdRef }} 颗能量石<br>请于指定地点兑换好礼
        </p>
      </div>
      <footer class="flex items-center justify-around gap-2 absolute bottom-5vw px-10vw w-full">
        <motion.img
          :src="btnCompleteIconUrl"
          alt="完成"
          class="h-auto w-34vw"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleComplete"
        />

        <motion.img
          v-if="isHasNextTaskRef"
          :src="btnNextIconUrl"
          alt="下一关卡"
          class="w-34vw h-auto"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleGoNext"
        />
      </footer>
    </div>
  </TheModal>
</template>

<style type="scss" scoped>
/* 按钮文字描边效果 */
.text-stroke-button {
  font-size: 3.5vw;
  font-weight: 600;
  text-shadow:
    1px -1px 0 #2a63b4,
    -1px 1px 0 #2a63b4,
    1px 1px 0 #2a63b4,
    -2px 0 0 #2a63b4,
    2px 0 0 #2a63b4,
    0 -2px 0 #2a63b4,
    0 2px 0 #2a63b4;
  letter-spacing: 0.5px;
}
</style>
