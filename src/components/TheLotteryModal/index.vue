<script setup lang="ts">
import { useModalEvents } from '@bryce-loskie/use'
// import LotteryGrid from 'lattice-lottery-new/LotteryGrid'
import { isLotteryModalVisibleRef } from '~/logic/lottery'

const { onOpen } = useModalEvents(isLotteryModalVisibleRef)

const handleClose = () => {
  isLotteryModalVisibleRef.value = false
}

const { data, isLoading, refetch } = useQuery({
  queryKey: ['lottery'],
  queryFn: () => miscApi.getPrizeList(),
  select: res => (res?.data || []).slice(0, 9).map(item => ({
    id: item.prizeId,
    name: item.prizeName,
    image: item.pic || '',
  })),
  enabled: false,
})

const resultId = ref('')

onOpen(async () => {
  await refetch()

  const [err, res] = await to(miscApi.drawPrize())
  if (err) {
    const idx = Math.floor(Math.random() * (data.value?.length || 1))
    resultId.value = data.value?.[idx]?.id || ''
    return
  }

  const presetResultId = res?.data?.prizeId
  if (presetResultId) {
    resultId.value = presetResultId
  }
  else {
    const idx = Math.floor(Math.random() * (data.value?.length || 1))
    resultId.value = data.value?.[idx]?.id || ''
  }
})

const isLotteryEnd = ref(false)

const lotteryResult = shallowRef<{
  id: string
  name: string
  image: string
}>()

const handleLotteryEnd = (id: string) => {
  isLotteryEnd.value = true

  const item = data.value?.find(item => item.id === id)
  if (item) {
    lotteryResult.value = item

    uni.showToast({
      title: `恭喜你抽中了 ${item.name}`,
      icon: 'success',
    })
  }
}
</script>

<template>
  <wd-popup
    v-model="isLotteryModalVisibleRef"
    safe-area-inset-bottom
    closable
    position="bottom"
    custom-class="rounded-t-32rpx"
    @close="handleClose"
  >
    <!-- Loading State -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-svg-spinners-ring-resize text-3xl text-blue-500 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        加载抽奖信息中...
      </p>
    </div>

    <div v-else-if="data" class="p-4 pt-10 h-fit overflow-auto rules-container scrollbar">
      <TheNineGridLottery :list="data" :result-id="resultId" @end="handleLotteryEnd" />
    </div>

    <div v-else class="fc py-20 px-6">
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        暂无抽奖信息
      </p>
    </div>
  </wd-popup>
</template>
