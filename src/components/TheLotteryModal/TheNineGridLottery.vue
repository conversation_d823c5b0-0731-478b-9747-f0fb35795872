<script setup lang="ts">
import { isRulesModalVisibleRef } from '~/logic/task.ts'

interface Props {
  list: {
    id: string
    name: string
    image: string
  }[]
  resultId?: string
  times?: number
}

const props = withDefaults(defineProps<Props>(), {
  resultId: '',
  times: 1,
})

const emit = defineEmits<{
  (e: 'end', id: string): void
}>()

// Animation state
const isSpinning = ref(false)
// const currentHighlightIndex = ref(-1)
const animationId = ref<number | null>(null)

// Arrange items in clockwise order: 1,2,3,8,center,4,7,6,5
// The grid positions correspond to: 0,1,2,7,center,3,6,5,4

// Get the arranged items in clockwise order
const arrangedItems = computed(() => {
  const items = [...props.list]
  // Ensure we have exactly 8 items for the grid (excluding center)
  while (items.length < 8) {
    items.push({ id: `empty-${items.length}`, name: '', image: '' })
  }
  return items.slice(0, 8)
})

// Find result index in clockwise order
const resultIndex = computed(() => {
  if (!props.resultId)
    return 0
  return arrangedItems.value.findIndex(item => item.id === props.resultId)
})

const indexArr = [0, 1, 2, 3, 4, 5, 6, 7]
const { state: currentHighlightIndex, next } = useCycleList(indexArr, {
  initialValue: 0,
})

const totalTimesRef = ref(props.times || 1)

// Start lottery animation
const startLottery = () => {
  if (isSpinning.value || resultIndex.value === -1)
    return

  if (totalTimesRef.value <= 0)
    return

  totalTimesRef.value--

  isSpinning.value = true
  currentHighlightIndex.value = 0

  let speed = 200 // Start slow (200ms)
  let totalSteps = 0
  const minSpeed = 50 // Fastest speed
  const maxSteps = 30 // Minimum steps before considering stopping

  const animate = () => {
    totalSteps++
    next(1)

    // Speed control: slow -> fast -> slow
    if (totalSteps < 10) {
      // Initial acceleration
      speed = Math.max(minSpeed, speed - 15)
    }
    else if (totalSteps > maxSteps && currentHighlightIndex.value === resultIndex.value) {
      // Stop at result after minimum steps
      isSpinning.value = false
      emit('end', props.list[resultIndex.value].id)
      return
    }
    else if (totalSteps > maxSteps - 8) {
      // Deceleration phase
      speed = Math.min(200, speed + 20)
    }

    if (animationId.value)
      clearTimeout(animationId.value)

    animationId.value = setTimeout(animate, speed)
  }

  animate()
}

// Stop animation
const stopAnimation = () => {
  if (animationId.value) {
    clearTimeout(animationId.value)
    animationId.value = null
  }
  isSpinning.value = false
  currentHighlightIndex.value = -1
}

// Cleanup on unmount
onBeforeUnmount(() => {
  stopAnimation()
})

const handleViewRules = () => {
  isRulesModalVisibleRef.value = true
}
</script>

<template>
  <div class="p-6 rounded-2xl text-white bg-#14AAFF">
    <h2 class="text-2xl font-bold text-center mb-6">
      抽奖
    </h2>

    <!-- 3x3 Grid -->
    <div class="aspect-square grid grid-cols-3 gap-2 max-w-sm mx-auto">
      <!-- Row 1: positions 0, 1, 2 -->
      <div
        v-for="index in [0, 1, 2]"
        :key="`grid-${index}`"
        class="aspect-square bg-white/10 rounded-lg p-2 border-2 transition-all duration-200"
        :class="currentHighlightIndex === index
          ? 'border-yellow-400 bg-yellow-400/20 scale-105 shadow-lg animate-pulse'
          : 'border-transparent'"
      >
        <div class="w-full h-full flex flex-col items-center justify-center">
          <img
            v-if="arrangedItems[index]?.image"
            :src="arrangedItems[index].image"
            :alt="arrangedItems[index].name"
            class="w-10 h-10 object-cover rounded"
          >
          <div v-else class="w-10 h-10 fc">
            <div class="i-tabler:gift text-lg" />
          </div>
          <span class="text-10px text-center text-pretty mt-1 leading-120% text-center ellipsis-2 w-full">{{ arrangedItems[index]?.name }}</span>
        </div>
      </div>

      <!-- Row 2: position 7, center button, position 3 -->
      <div
        class="aspect-square bg-white/10 rounded-lg p-2 border-2 transition-all duration-200"
        :class="currentHighlightIndex === 7
          ? 'border-yellow-400 bg-yellow-400/20 scale-105 shadow-lg animate-pulse'
          : 'border-transparent'"
      >
        <div class="w-full h-full flex flex-col items-center justify-center">
          <img
            v-if="arrangedItems[7]?.image"
            :src="arrangedItems[7].image"
            :alt="arrangedItems[7].name"
            class="w-10 h-10 object-cover rounded"
          >
          <div v-else class="w-10 h-10 fc">
            <div class="i-tabler:gift text-lg" />
          </div>
          <span class="text-10px text-center text-pretty leading-120% mt-1 text-center ellipsis-2 w-full">{{ arrangedItems[7]?.name }}</span>
        </div>
      </div>

      <!-- Center Button -->
      <button
        :disabled="isSpinning || !resultId || totalTimesRef <= 0"
        class="aspect-square rounded-lg font-bold text-white text-lg transition-all duration-200 flex items-center justify-center"
        :class="[
          isSpinning ? 'bg-gray-400' : 'bg-red-500',
          !isSpinning && !props.resultId ? 'bg-gray-400' : '',
          !isSpinning && props.resultId ? 'hover:bg-red-600 hover:scale-105 hover:shadow-lg' : '',
          totalTimesRef <= 0 ? 'bg-gray-400' : '',
        ]"
        @click="startLottery"
      >
        <span v-if="!isSpinning" class="leading-120%" :class="totalTimesRef > 0 ? 'text-sm' : 'text-10px'">
          <pre>{{ totalTimesRef > 0 ? '开始' : '抽奖次数\n已用完' }}</pre>
        </span>
        <span v-else class="i-svg-spinners:blocks-shuffle-3 text-lg" />
      </button>

      <div
        class="aspect-square bg-white/10 rounded-lg p-2 border-2 transition-all duration-200"
        :class="currentHighlightIndex === 3
          ? 'border-yellow-400 bg-yellow-400/20 scale-105 shadow-lg animate-pulse'
          : 'border-transparent'"
      >
        <div class="w-full h-full flex flex-col items-center justify-center">
          <img
            v-if="arrangedItems[3]?.image"
            :src="arrangedItems[3].image"
            :alt="arrangedItems[3].name"
            class="w-10 h-10 object-cover rounded"
          >
          <div v-else class="w-10 h-10 fc">
            <div class="i-tabler:gift text-lg" />
          </div>
          <span class="text-10px text-center text-pretty leading-120% mt-1 text-center ellipsis-2 w-full">{{ arrangedItems[3]?.name }}</span>
        </div>
      </div>

      <!-- Row 3: positions 6, 5, 4 -->
      <div
        v-for="index in [6, 5, 4]"
        :key="`grid-${index}`"
        class="aspect-square bg-white/10 rounded-lg p-2 border-2 transition-all duration-200"
        :class="currentHighlightIndex === index
          ? 'border-yellow-400 bg-yellow-400/20 scale-105 shadow-lg animate-pulse'
          : 'border-transparent'"
      >
        <div class="w-full h-full flex flex-col items-center justify-center">
          <img
            v-if="arrangedItems[index]?.image"
            :src="arrangedItems[index].image"
            :alt="arrangedItems[index].name"
            class="w-10 h-10 object-cover rounded"
          >
          <div v-else class="w-10 h-10 fc">
            <div class="i-tabler:gift text-lg" />
          </div>
          <span class="text-10px text-center text-pretty leading-120% mt-1 text-center ellipsis-2 w-full">{{ arrangedItems[index]?.name }}</span>
        </div>
      </div>
    </div>
    <div align="right">
      <p style="font-size: 8px" @onclick="handleViewRules">
        抽奖规则
      </p>
    </div>

    <TheRuleModal />
  </div>
</template>
