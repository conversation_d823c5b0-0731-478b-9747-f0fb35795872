<script setup lang="ts">
import { useModalEvents } from '@bryce-loskie/use'
import { useQueryClient } from '@tanstack/vue-query'
import { currentMissionIdRef, isMissionModalVisibleRef, MissionComponentMap } from '~/logic/stage'
import { isTakePhotoModalVisibleRef, isTaskCompleteModalVisibleRef } from '~/logic/task'

const { onOpen, onClose } = useModalEvents(isMissionModalVisibleRef)

const userStore = useUserStore()

onClose(() => {
  userStore.m = undefined // reset m
})

const { isLoading, data: missionInfoRef, refetch } = useQuery({
  queryKey: [currentMissionIdRef, 'mession-detail'],
  queryFn: ({ queryKey: [missionId] }) => miscApi.getMissionInfo(missionId),
  enabled: false,
  select: data => data?.data ?? null,
})

onOpen(() => {
  refetch()
})

const handleClose = () => {
  isMissionModalVisibleRef.value = false
}

const queryClient = useQueryClient()

const { mutate: completeMission } = useMutation({
  mutationFn: (missionId: string) => miscApi.completeMission(missionId),
  onSuccess: async () => {
    // 先刷新任务列表数据
    await queryClient.invalidateQueries({ queryKey: miscApi.getUserMissionListQueryKey })

    // 获取最新的任务完成状态
    const updatedMissionData = await queryClient.fetchQuery({
      queryKey: miscApi.getUserMissionListQueryKey,
      queryFn: miscApi.getUserMissionStatus,
    })

    const missionInfos = updatedMissionData?.data?.missionInfos || []

    // 判断是否所有任务都已完成（排除入口任务）
    const allTasksCompleted = missionInfos.length > 0 && missionInfos.every(mission => mission.isFinished)

    isMissionModalVisibleRef.value = false
    console.log('allTasksCompleted', allTasksCompleted, missionInfos)
    if (allTasksCompleted) {
      // 任务全部完成，打开拍照任务
      isTakePhotoModalVisibleRef.value = true
    }
    else {
      // 还有任务未完成，显示任务完成弹窗
      isTaskCompleteModalVisibleRef.value = true
    }
  },
  onError: (error: any) => {
    console.error(error)
    uni.showToast({
      title: error?.message ?? '任务完成失败, 请稍后重试',
      icon: 'error',
    })
  },
})
</script>

<template>
  <wd-popup
    v-model="isMissionModalVisibleRef"
    safe-area-inset-bottom
    closable
    position="bottom"
    custom-class="rounded-t-32rpx"
    @close="handleClose"
  >
    <!-- Loading State -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 px-6">
      <div class="i-svg-spinners-ring-resize text-3xl text-blue-500 mb-4" />
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        加载任务中...
      </p>
    </div>

    <div v-else-if="missionInfoRef && isMissionModalVisibleRef" class="p-4 max-h-80vh min-h-sm h-fit overflow-auto scrollbar flex flex-col gap-4">
      <h1 class="text-lg font-bold text-center">
        {{ missionInfoRef.missionName }}
      </h1>

      <Suspense>
        <component
          :is="MissionComponentMap[missionInfoRef.missionType || 'scan']"
          :mission="missionInfoRef"
          @complete="completeMission(missionInfoRef.missionId)"
        />
      </Suspense>
    </div>

    <div v-else class="fc py-20 px-6">
      <p class="text-gray-600 dark:text-gray-400 text-sm">
        暂无任务
      </p>
    </div>
  </wd-popup>
</template>
