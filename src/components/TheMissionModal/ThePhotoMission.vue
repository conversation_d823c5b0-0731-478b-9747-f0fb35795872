<script setup lang="ts">
import type { IMission } from '~/api/misc'

interface Props {
  mission: IMission
}

interface PhotoData {
  title: string
  photo: string
  question: string
  intro: string
  info: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  complete: [missionId: string]
}>()

// Parse mission data
const missionData = computed<PhotoData>(() => {
  try {
    return typeof props.mission.missionInfo === 'string'
      ? JSON.parse(props.mission.missionInfo)
      : props.mission.missionInfo
  }
  catch {
    return { title: '', photo: '', question: '', intro: '', info: '' }
  }
})

const imageUrlRef = ref<string>()
const isUploading = ref(false)
const isCompleted = ref(false)
const missionId = computed(() => props.mission?.missionId ?? '')

// 水母任务相关状态
const selectedImageIndex = ref<number | null>(null)
const isSubmitting = ref(false)

// 水母任务图片链接
const jellyFishImages = [
  'https://tjlg.haiheangroup.com/image/shuimu1.jpg',
  'https://tjlg.haiheangroup.com/image/shuimu2.jpg',
]

const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['camera'],
    success(res) {
      console.log('success', res)
      const tempFilePaths = res.tempFilePaths
      if (tempFilePaths && tempFilePaths.length > 0) {
        imageUrlRef.value = tempFilePaths[0]
        // uni.showToast({
        //   title: '拍照成功',
        //   icon: 'success',
        // })
      }
    },
    fail(err) {
      console.error('拍照失败:', err)
      // let errorMsg = '拍照失败'
      //
      // // 根据不同错误类型给出不同提示
      // if (err.errMsg) {
      //   if (err.errMsg.includes('cancel')) {
      //     errorMsg = '用户取消拍照'
      //   }
      //   else if (err.errMsg.includes('permission')) {
      //     errorMsg = '请授权相机权限'
      //   }
      //   else if (err.errMsg.includes('system')) {
      //     errorMsg = '系统错误，请重试'
      //   }
      // }

      // uni.showToast({
      //   title: errorMsg,
      //   icon: 'none',
      // })
    },
  })
}

const uploadAndComplete = async () => {
  if (!imageUrlRef.value || isUploading.value)
    return

  isUploading.value = true

  try {
    uni.showLoading({ title: '上传照片中...' })

    // For uni-app, we need to create a File object from the temp file path
    // This is a simplified version - in real app you might need to handle this differently
    const blob = await fetch(imageUrlRef.value).then(r => r.blob())
    const file = new File([blob], `photo_${Date.now()}.jpg`, { type: 'image/jpeg' })

    const { url: _uploadedUrl } = await uploadFile({
      file,
      onProgress(progressData) {
        uni.showToast({
          title: `上传进度: ${progressData.percent.toFixed(0)}%`,
          icon: 'none',
        })
      },
    })

    uni.hideLoading()
    // uni.showToast({
    //   title: '上传成功！',
    //   icon: 'success',
    // })

    isCompleted.value = true

    // Complete mission after short delay
    await sleep(1500)
    emit('complete', props.mission?.missionId ?? '')
  }
  catch (error) {
    uni.hideLoading()
    console.error('Upload failed:', error)
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'error',
    })
  }
  finally {
    isUploading.value = false
  }
}

const retakePhoto = () => {
  imageUrlRef.value = undefined
  isCompleted.value = false
}

// 水母任务相关函数
const selectJellyFishImage = (index: number) => {
  selectedImageIndex.value = index
}

const submitJellyFishChoice = async () => {
  if (selectedImageIndex.value === null || isSubmitting.value)
    return

  isSubmitting.value = true

  try {
    uni.showLoading({ title: '提交中...' })

    // 模拟提交延迟
    await sleep(1000)

    uni.hideLoading()
    isCompleted.value = true

    // Complete mission after short delay
    await sleep(1500)
    emit('complete', props.mission?.missionId ?? '')
  }
  catch (error) {
    uni.hideLoading()
    console.error('Submit failed:', error)
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'error',
    })
  }
  finally {
    isSubmitting.value = false
  }
}

const resetJellyFishChoice = () => {
  selectedImageIndex.value = null
  isCompleted.value = false
}
</script>

<template>
  <div class="photo-mission min-h-full">
    <!-- Header -->
    <div class="p-2">
      <div class="flex items-center gap-3 mb-4">
        <div class="w-10 h-10 bg-purple-500 rounded-full flex items-center justify-center">
          <div class="i-material-symbols-photo-camera text-white text-xl shrink-0" />
        </div>
        <div>
          <h2 class="text-sm font-bold text-gray-900 dark:text-white">
            任务类型
          </h2>
          <p class=" text-gray-600 dark:text-gray-400">
            {{ missionData.intro }}
          </p>
        </div>
      </div>

      <!-- Mission Title -->
      <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2   shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-camera-enhance text-purple-500 text-lg shrink-0" />
          </div>
          <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
            {{ missionData.title }}
          </p>
        </div>
      </div>
    </div>

    <!-- Photo Content -->
    <div class="p-2">
      <!-- Task Instructions -->
      <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-task-alt text-purple-600 dark:text-purple-400 text-lg" />
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">
              互动说明
            </h3>
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty">
              {{ missionData.info }}
            </p>
            <!--            <img -->
            <!--              :src="{{ missionData.photo }}" -->
            <!--              class="w-full h-80 object-cover rounded-lg border-2 border-gray-200 dark:border-gray-600 shrink-0" -->
            <!--            > -->
          </div>
        </div>
      </div>

      <!-- Question -->
      <div class="bg-white dark:bg-gray-800 rounded-xl py-4 px-2 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="flex items-start gap-3">
          <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center shrink-0">
            <div class="i-material-symbols-help text-blue-600 dark:text-blue-400 text-lg shrink-0" />
          </div>
          <div class="flex-1">
            <p class="text-gray-700 dark:text-gray-300 leading-relaxed text-pretty mb-3">
              {{ missionData.question }}
            </p>
            <!-- 显示任务相关图片 -->
            <img
              v-if="missionData.photo"
              :src="missionData.photo"
              class="w-full h-80 object-cover rounded-lg border-2 border-gray-200 dark:border-gray-600 shrink-0"
              alt="任务图片"
            >
          </div>
        </div>
      </div>

      <!-- 水母任务特殊处理 -->
      <div v-if="missionId === '5'" class="pb-6">
        <!-- 图片选择区域 -->
        <div class="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center shrink-0">
              <div class="i-material-symbols-image text-blue-500 text-lg shrink-0" />
            </div>
            <span>请选择其中一个图片</span>
          </h4>

          <!-- 图片选项 -->
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div
              v-for="(imageUrl, index) in jellyFishImages"
              :key="index"
              class="jellyfish-option"
              :class="{ selected: selectedImageIndex === index }"
              @click="selectJellyFishImage(index)"
            >
              <img
                :src="imageUrl"
                :alt="`水母选项 ${index + 1}`"
                class="w-full h-40 object-cover rounded-lg"
              >
              <div v-if="selectedImageIndex === index" class="selection-indicator">
                <div class="i-material-symbols-check-circle text-2xl text-white" />
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-col gap-3">
          <!-- 提交按钮 -->
          <button
            v-if="selectedImageIndex !== null && !isCompleted"
            :disabled="isSubmitting"
            class="photo-btn bg-blue-500 hover:bg-blue-600"
            @click="submitJellyFishChoice"
          >
            <div v-if="isSubmitting" class="i-svg-spinners-ring-resize text-lg mr-2" />
            <div v-else class="i-material-symbols-send text-lg mr-2" />
            {{ isSubmitting ? '提交中...' : '提交选择' }}
          </button>

          <!-- 重新选择按钮 -->
          <button
            v-if="selectedImageIndex !== null && !isCompleted"
            class="photo-btn-secondary"
            @click="resetJellyFishChoice"
          >
            <div class="i-material-symbols-refresh text-lg mr-2" />
            重新选择
          </button>

          <!-- 成功状态 -->
          <div v-if="isCompleted" class="success-message">
            <div class="i-material-symbols-celebration text-2xl mb-2" />
            <p class="font-semibold mb-1 text-sm">
              选择完成！
            </p>
            <p class="opacity-80 text-12px">
              即将进入下一关...
            </p>
          </div>
        </div>
      </div>

      <div v-else class="pb-6">
        <!-- Photo Preview Area -->
        <div class="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <h4
            class="font-semibold text-gray-900 dark:text-white flex items-center gap-2"
            :class="[imageUrlRef ? 'mb-3' : '']"
          >
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center shrink-0">
              <div class="i-material-symbols-image text-purple-500 text-lg shrink-0" />
            </div>
            <span>
              {{ imageUrlRef ? '拍摄的照片' : '等待拍照' }}
            </span>
          </h4>

          <div v-if="imageUrlRef" class="photo-preview-area">
            <!-- Photo Preview -->
            <div v-if="imageUrlRef" class="relative">
              <img
                :src="imageUrlRef"
                class="w-full h-80 object-cover rounded-lg border-2 border-gray-200 dark:border-gray-600 shrink-0"
                alt="拍摄的照片"
              >
            <!--            <div v-if="isCompleted" class="absolute inset-0 bg-green-500/20 rounded-lg flex items-center justify-center"> -->
            <!--              <div class="bg-green-500 text-white px-4 py-2 rounded-full flex items-center gap-2"> -->
            <!--                <div class="i-material-symbols-check-circle text-lg" /> -->
            <!--                <span class="font-medium">上传成功！</span> -->
            <!--              </div> -->
            <!--            </div> -->
            </div>

            <!-- Empty State -->
            <div v-else class="photo-placeholder">
              <div class="i-material-symbols-add-a-photo text-4xl text-gray-300 dark:text-gray-600 mb-3" />
              <p class="text-gray-500 dark:text-gray-400 text-12px text-center">
                点击下方按钮开始拍照
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col gap-3">
          <!-- Take Photo Button -->
          <button
            v-if="!imageUrlRef"
            class="photo-btn bg-purple-500 hover:bg-purple-600"
            @click="takePhoto"
          >
            <div class="i-material-symbols-photo-camera text-xl mr-2" />
            开始拍照
          </button>

          <!-- Photo Actions -->
          <div v-else-if="!isCompleted" class="flex gap-3">
            <button
              class="photo-btn-secondary flex-1"
              @click="retakePhoto"
            >
              <div class="i-material-symbols-refresh text-lg mr-2" />
              重拍
            </button>

            <button
              :disabled="isUploading"
              class="photo-btn bg-green-500 hover:bg-green-600 flex-1"
              @click="uploadAndComplete"
            >
              <div v-if="isUploading" class="i-svg-spinners-ring-resize text-lg mr-2" />
              <div v-else class="i-material-symbols-cloud-upload text-lg mr-2" />
              {{ isUploading ? '上传中...' : '上传' }}
            </button>
          </div>

          <!-- Success State -->
          <div v-else class="success-message">
            <div class="i-material-symbols-celebration text-2xl mb-2" />
            <p class="font-semibold mb-1 text-sm">
              拍照任务完成！
            </p>
            <p class="opacity-80 text-12px">
              即将进入下一关...
            </p>
          </div>
        </div>
      </div>

      <!-- No Data State -->
      <div v-if="!missionData.title && !missionData.question" class="flex flex-col items-center justify-center py-20 px-6">
        <div class="i-material-symbols-photo-camera text-4xl text-gray-400 mb-4" />
        <p class="text-gray-600 dark:text-gray-400 text-center">
          暂无拍照任务数据
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.photo-preview-area {
  @apply min-h-64 flex items-center justify-center;
}

.photo-placeholder {
  @apply w-full h-64 bg-gray-50 dark:bg-gray-700 border-2 border-dashed
         border-gray-300 dark:border-gray-600 rounded-lg
         flex flex-col items-center justify-center;
}

.photo-btn {
  @apply w-full py-2 px-6 text-white font-medium rounded-xl shadow-lg
         transition-all duration-200 transform hover:scale-105 active:scale-95
         disabled:cursor-not-allowed disabled:hover:scale-100 disabled:opacity-50
         flex items-center justify-center outline-none border-none;
}

.photo-btn-secondary {
  @apply w-full py-2 px-6 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-xl shadow-lg
         transition-all duration-200 transform hover:scale-105 active:scale-95
         flex items-center justify-center outline-none border-none;
}

.success-message {
  @apply text-center text-green-600 dark:text-green-400
         bg-green-50 dark:bg-green-900/20 rounded-xl px-6 py-4
         border border-green-200 dark:border-green-800;
}

/* 水母任务样式 */
.jellyfish-option {
  @apply relative cursor-pointer transition-all duration-200 transform hover:scale-105;
  @apply border-2 border-transparent rounded-lg overflow-hidden;
}

.jellyfish-option.selected {
  @apply border-blue-500 shadow-lg;
}

.selection-indicator {
  @apply absolute top-2 right-2 w-8 h-8 bg-blue-500 rounded-full
         flex items-center justify-center shadow-lg;
}
</style>
