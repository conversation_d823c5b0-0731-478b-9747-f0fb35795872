<script setup lang="ts">
// import { useQueryClient } from '@tanstack/vue-query'
import { motion } from 'motion-v'
import { modalBgImageUrl, modalStartIcon } from '~/logic/preload-assets'
// import { currentMissionIdRef, isMissionModalVisibleRef, StateEnum } from '~/logic/stage'
import { isTaskStartModalVisibleRef } from '~/logic/task'

const modalBgImageRef = ref<HTMLDivElement>()
const modalBgImageHeightRef = ref(0)
watchEffect(() => {
  modalBgImageHeightRef.value = modalBgImageRef.value?.clientHeight ?? 0
})

// const queryClient = useQueryClient()
const { mutate: handleStartAdventure } = useMutation({
  mutationFn: (missionId: string) => miscApi.completeMission(missionId),
  onSuccess: () => {
    localStorage.setItem('hasStarted', 'true')
    isTaskStartModalVisibleRef.value = false
    // isMissionModalVisibleRef.value = true
    // currentMissionIdRef.value = StateEnum.Stage1 // set as first stage
    // queryClient.invalidateQueries({ queryKey: miscApi.getUserMissionListQueryKey })
  },
  onError: (error: any) => {
    console.error(error)
    uni.showToast({
      title: error?.message ?? '任务完成失败, 请稍后重试',
      icon: 'error',
    })
  },
})

const dummyStageDesc = `
<p>在天津极地海洋世界的深海深处，龙宫能量突然紊乱，七颗维系海洋和平的“龙宫能量石”四散在园区七大秘境之中！</p>
<p>🌟龙王发出召集令，寻找真正勇敢又聪明的小探险家，重启能量，守护龙宫！</p>
<p>📍每完成一个秘境任务，就能点亮一颗能量石</p>
<p>🔋集齐3颗能量石，可兑换基础奖励</p>
<p>🔮集齐7颗能量石，即可获得「龙王亲封能量勋章」！</p>
<p>现在就启程吧，穿越企鹅湾、白鲸水殿、水母幻光洞……</p>
<p>探寻每一处秘境的能量源，成为天津龙宫的守护者！</p>
<p>✅互动说明</p>
<p>- 点击地图图标查看任务详情</p>
<p>- 每完成一处挑战，即点亮一颗能量石</p>
<p>- 能量越多，奖励越丰富，快去探索吧！</p>
`.trim()

const modalStageDescRef = ref<string>()

const init = () => {
  modalStageDescRef.value = dummyStageDesc
}

init()
</script>

<template>
  <TheModal v-model:visible="isTaskStartModalVisibleRef" width="90vw">
    <div
      ref="modalBgImageRef"
      class="w-full bg-image aspect-9/12 relative"
      :style="{
        backgroundImage: `url(${modalBgImageUrl})`,
      }"
    >
      <div class="flex flex-col gap-4 items-center w-full pl-16% pr-10% mt-50%">
        <div
          :style="{
            maxHeight: `${modalBgImageHeightRef * 0.37}px`,
          }"
          class="flex flex-col gap-1 text-sm whitespace-pre-wrap indent-2em overflow-y-auto overscroll-contain"
          v-html="modalStageDescRef"
        />
      </div>

      <div class="w-full h-auto cursor-pointer fc absolute bottom-8% left-0 pl-6%">
        <motion.img
          :src="modalStartIcon"
          alt="开启探险之旅"
          class="w-50vw h-auto"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleStartAdventure('0')"
        />
      </div>
    </div>
  </TheModal>
</template>
