<script setup lang="ts">
import { AnimatePresence, motion } from 'motion-v'
import { modalCloseIcon } from '~/logic/preload-assets'

interface Props {
  width?: string
  gap?: string
  closeable?: boolean
}

withDefaults(defineProps<Props>(), {
  width: '90vw',
  gap: '1.25rem',
  closeable: true,
})

const visibleRef = defineModel<boolean>('visible', { required: false, default: false })
</script>

<template>
  <AnimatePresence>
    <motion.div
      v-if="visibleRef"
      role="dialog-backdrop"
      aria-modal="true"
      aria-labelledby="modal-backdrop"
      class="fixed inset-0 z-50 bg-black/80"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :transition="{ duration: 0.3 }"
    />

    <motion.div
      v-if="visibleRef"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      class="fixed inset-0 z-51"
      :initial="{ opacity: 0, scale: 0 }"
      :animate="{ opacity: 1, scale: 1 }"
      :exit="{ opacity: 0, scale: 0 }"
    >
      <section
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center"
        :style="{
          width,
          gap,
        }"
      >
        <div class="w-full h-full flex flex-col items-center">
          <slot />
        </div>

        <motion.img
          v-if="closeable"
          :src="modalCloseIcon"
          alt="关闭"
          class="w-8 h-auto cursor-pointer"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="visibleRef = false"
        />
      </section>
    </motion.div>
  </AnimatePresence>
</template>
