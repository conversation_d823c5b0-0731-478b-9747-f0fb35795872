<script setup lang="ts">
import { AnimatePresence, motion } from 'motion-v'
import { modalCloseIcon } from '~/logic/preload-assets'

interface Props {
  width?: string
  gap?: string
  destroy?: () => void
  close?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  width: '90vw',
  gap: '1.25rem',
})

// 函数式调用时默认显示
const visibleRef = ref(true)

// 处理关闭逻辑
const handleClose = () => {
  visibleRef.value = false
  // 延迟执行销毁，让动画完成
  setTimeout(() => {
    props.close?.()
    props.destroy?.()
  }, 300)
}

// 暴露 beforeClose 方法供 createFuncComp 调用
defineExpose({
  beforeClose: () => {
    visibleRef.value = false
  },
})
</script>

<template>
  <AnimatePresence>
    <motion.div
      v-if="visibleRef"
      role="dialog-backdrop"
      aria-modal="true"
      aria-labelledby="modal-backdrop"
      class="fixed inset-0 z-50 bg-black/80"
      :initial="{ opacity: 0 }"
      :animate="{ opacity: 1 }"
      :exit="{ opacity: 0 }"
      :transition="{ duration: 0.3 }"
    />

    <motion.div
      v-if="visibleRef"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      class="fixed inset-0 z-51"
      :initial="{ opacity: 0, scale: 0 }"
      :animate="{ opacity: 1, scale: 1 }"
      :exit="{ opacity: 0, scale: 0 }"
    >
      <section
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex flex-col items-center"
        :style="{
          width,
          gap,
        }"
      >
        <div class="w-full h-full flex flex-col items-center">
          <slot />
        </div>

        <motion.img
          :src="modalCloseIcon"
          alt="关闭"
          class="w-8 h-auto cursor-pointer"
          :while-hover="{ scale: 1.1 }"
          :while-press="{ scale: 0.9 }"
          @click="handleClose"
        />
      </section>
    </motion.div>
  </AnimatePresence>
</template>
